@extends('layouts.partials.main')

@section('title', 'Review Content')

@section('content')
{{-- Head Content --}}
{{-- Banner Multiple Selection --}}
<div id="banner-container" class="z-50 hidden items-center fixed top-[135px] right-0 h-16 bg-neutral-600 shadow-md">
  <div class="container-fluid px-4 flex items-center justify-between h-full w-full">
    <div class="flex items-center gap-2 md:gap-4">
      <button class="btn btn-icon btn-sm bg-neutral-700 hover:bg-neutral-800" id="btn-close-banner">
        <i class="ki-filled ki-cross text-white"></i>
      </button>
      <span class="text-white font-medium text-sm md:text-base" id="banner-title"></span>
    </div>
    <div class="flex items-center">
      <button class="btn btn-sm bg-red-500 hover:bg-red-600 p-2 flex flex-row items-center gap-2 mr-4" id="btn-bulk-delete">
        <i class="ki-filled ki-trash text-white"></i>
        <span class="text-white text-sm md:text-base">Hapus</span>
      </button>
    </div>
  </div>
</div>
{{-- End of Banner Multiple Selection --}}
@php
  $user = auth()->user();
  $role_id = $user->role_id ?? null;

  // Check permissions for each tab - using the correct permission names
  // Main permission for the page
  $hasViewPermission = $user && $user->hasPermissions('reviews', $role_id);

  // Check if user has View permission (this is the main checkbox)
  $canViewAll = $hasViewPermission; // The 'reviews' permission is already the View permission

  // Individual tab permissions - only check if user has main View permission
  // Users need 'approve.reviews' permission to view pending items (since they need to be able to approve them)
  $canViewPending = $canViewAll && $user->hasPermissions('approve.reviews', $role_id);
  // Users need 'reject.reviews' permission to view rejected items
  $canViewRejected = $canViewAll && $user->hasPermissions('reject.reviews', $role_id);
  // Users need 'approve.reviews' permission to view approved items
  $canViewApproved = $canViewAll && $user->hasPermissions('approve.reviews', $role_id);
  // Users need 'publish.reviews' permission to view published items
  $canViewPublished = $canViewAll && $user->hasPermissions('publish.reviews', $role_id);

  // Get tab from URL parameter
  $tabParam = request()->query('tab');

  // Map URL parameter to status
  $tabStatusMap = [
    'pending' => 'Pending',
    'rejected' => 'Rejected',
    'approved' => 'Approved',
    'published' => 'Published'
  ];

  // Set active tab based on URL parameter or default
  $activeTab = 'pending';
  if ($tabParam && isset($tabStatusMap[$tabParam])) {
    $activeTab = $tabParam;
  } else {
    // Default tab based on permissions if no tab parameter
    if (!$canViewPending) {
      if ($canViewRejected) $activeTab = 'rejected';
      else if ($canViewApproved) $activeTab = 'approved';
      else if ($canViewPublished) $activeTab = 'published';
    }
  }

  // Set the status based on the active tab
  $status = isset($tabStatusMap[$activeTab]) ? $tabStatusMap[$activeTab] : 'Pending';
@endphp

<div class="mb-2">
  <!-- Container -->
  <div id="page-title" class="container-fixed items-center justify-between flex-wrap gap-5 lg:gap-0">
    <div class="flex flex-col mb-2 justify-center items-start flex-wrap gap-1 lg:gap-2">
      <h1 class="font-medium text-lg text-gray-900">
      {{ @$page }} - <span id="page-title-tab">{{ ucfirst($activeTab) }}</span>
      @php
        $badgeClass = 'badge-dark';
        if ($activeTab == 'pending') $badgeClass = 'badge-warning';
        else if ($activeTab == 'rejected') $badgeClass = 'badge-danger';
        else if ($activeTab == 'approved') $badgeClass = 'badge-primary';
        else if ($activeTab == 'published') $badgeClass = 'badge-success';
      @endphp
      <span class="badge badge-outline {{ $badgeClass }} ml-2" id="page-title-badge"></span>
      </h1>
    </div>
  </div>
  <!-- End of Container -->
</div>
{{-- End of head content --}}

<!-- Container -->
<div class="container-fixed max-h-fit">
  <div class="flex items-center flex-wrap md:flex-nowrap lg:items-end justify-between border-b border-b-gray-200 dark:border-b-coal-100 gap-3 lg:gap-6 mb-5 lg:mb-6">
    <div class="grid">
    <div class="scrollable-x-auto">
      <div class="tabs gap-5 hidden" data-tabs="false" id="my_tabs">

      @if($canViewAll)
        @if($canViewPending)
        <button class="tab {{ $activeTab == 'pending' ? 'active' : '' }}" data-tab-toggle="#tab_pending" data-status="Pending">
          <span class="menu-title text-nowrap text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-medium menu-item-here:text-primary menu-item-here:font-medium menu-item-show:text-primary menu-link-hover:text-primary">
            Pending <span class="badge badge-outline badge-warning ml-2" id="tab-item-pending"></span>
          </span>
        </button>
        @endif

        @if($canViewRejected)
        <button class="tab {{ $activeTab == 'rejected' ? 'active' : '' }}" data-tab-toggle="#tab_rejected" data-status="Rejected">
          <span class="menu-title text-nowrap text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-medium menu-item-here:text-primary menu-item-here:font-medium menu-item-show:text-primary menu-link-hover:text-primary">
            Rejected <span class="badge badge-outline badge-danger ml-2" id="tab-item-rejected"></span>
          </span>
        </button>
        @endif

        @if($canViewApproved)
        <button class="tab {{ $activeTab == 'approved' ? 'active' : '' }}" data-tab-toggle="#tab_approved" data-status="Approved">
          <span class="menu-title text-nowrap text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-medium menu-item-here:text-primary menu-item-here:font-medium menu-item-show:text-primary menu-link-hover:text-primary">
            Approved <span class="badge badge-outline badge-primary ml-2" id="tab-item-approved"></span>
          </span>
        </button>
        @endif

        @if($canViewPublished)
        <button class="tab {{ $activeTab == 'published' ? 'active' : '' }}" data-tab-toggle="#tab_published" data-status="Published">
          <span class="menu-title text-nowrap text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-medium menu-item-here:text-primary menu-item-here:font-medium menu-item-show:text-primary menu-link-hover:text-primary">
            Published <span class="badge badge-outline badge-success ml-2" id="tab-item-published"></span>
          </span>
        </button>
        @endif
      @else
        <div class="p-4">
          <p class="text-gray-600">No tabs available. Please check your permissions.</p>
        </div>
      @endif
      </div>
    </div>
    </div>
  </div>
  <!-- begin: toolbar -->
  <div class="flex flex-wrap items-center gap-5 mb-4 justify-center lg:justify-between">
    <div class="flex flex-wrap items-center gap-2 lg:gap-5">

      <div role="status" class="space-y-2.5 animate-pulse max-w-lg toolbar-skeleton hidden">
        <div class="flex items-center w-full">
          <div class="h-2.5 bg-gray-200 rounded-full dark:bg-gray-600 w-60"></div>
          <div class="h-2.5 ms-2 bg-gray-200 rounded-full dark:bg-gray-700 w-40"></div>
        </div>
      </div>

      <h3 class="text-md lg:text-lg text-gray-900 font-semibold" id="tab-note"></h3>
      <div class="menu menu-default hidden" data-menu="true" id="filter-menu">
        <div class="menu-item" data-menu-item-offset="15, 0" data-menu-item-placement="bottom-end" data-menu-item-toggle="dropdown" data-menu-item-trigger="hover">
         <button class="menu-toggle btn btn-light btn-sm flex-nowrap" id="btn-filter"></button>
         <div class="menu-dropdown w-40 py-2 max-h-[250px]">
          <div class="menu-item filter-media active" data-filter="index">
           <button type="button" class="menu-link">
            <span class="flex items-center me-2">
              <i class="fa-solid fa-list"></i>
            </span>
            <span class="menu-title" id="filter-item-index"></span>
           </button>
          </div>
          <div class="menu-item filter-media" data-filter="image">
           <button type="button" class="menu-link">
            <span class="flex items-center me-2">
              <i class="fa-solid fa-image"></i>
            </span>
            <span class="menu-title" id="filter-item-image"></span>
           </button>
          </div>
          <div class="menu-item filter-media" data-filter="video">
           <button type="button" class="menu-link">
            <span class="flex items-center me-2">
              <i class="fa-solid fa-video"></i>
            </span>
            <span class="menu-title" id="filter-item-video"></span>
           </button>
          </div>
          <div class="menu-item filter-media" data-filter="audio">
           <button type="button" class="menu-link">
            <span class="flex items-center me-2">
              <i class="fa-solid fa-music"></i>
            </span>
            <span class="menu-title" id="filter-item-audio"></span>
           </button>
          </div>
          <div class="menu-item filter-media" data-filter="document">
            <button type="button" class="menu-link">
             <span class="flex items-center me-2">
               <i class="fa-solid fa-file"></i>
             </span>
             <span class="menu-title" id="filter-item-document"></span>
            </button>
           </div>
         </div>
        </div>
      </div>
      <div class="menu menu-default hidden" data-menu="true" id="flag-menu">
        <div class="menu-item" data-menu-item-offset="15, 0" data-menu-item-placement="bottom-end" data-menu-item-toggle="dropdown" data-menu-item-trigger="hover">
         <button class="menu-toggle btn btn-light btn-sm flex-nowrap" id="btn-flag"></button>
         <div class="menu-dropdown w-36 py-2 max-h-[250px]">
          <div class="menu-item flag active" data-flag="Reviewed">
           <button type="button" class="menu-link">
            <span class="menu-title" id="flag-item-reviewed"></span>
           </button>
          </div>
          <div class="menu-item flag" data-flag="Approved">
           <button type="button" class="menu-link">
            <span class="menu-title" id="flag-item-approved"></span>
           </button>
          </div>
          <div class="menu-item flag" data-flag="Rejected">
           <button type="button" class="menu-link">
            <span class="menu-title" id="flag-item-rejected"></span>
           </button>
          </div>
         </div>
        </div>
      </div>

    </div>
    <nav aria-label="Page navigation example">
      <ul class="flex items-center -space-x-px h-8 text-sm" id="pagination-container"></ul>
    </nav>
  </div>
  <!-- end: toolbar -->
</div>
<!-- End of Container -->


{{-- Review Tabs --}}
@if($canViewAll)
  @if($canViewPending)
  <div class="transition-opacity duration-700 {{ $activeTab != 'pending' ? 'hidden' : '' }}" id="tab_pending">
  @include('pages.review-content.tabs.pending')
  </div>
  @endif

  {{-- <div class="hidden transition-opacity duration-700" id="tab_reviewed">
  @include('pages.review-content.tabs.reviewed')
  </div> --}}

  @if($canViewRejected)
  <div class="transition-opacity duration-700 {{ $activeTab != 'rejected' ? 'hidden' : '' }}" id="tab_rejected">
  @include('pages.review-content.tabs.rejected')
  </div>
  @endif

  @if($canViewApproved)
  <div class="transition-opacity duration-700 {{ $activeTab != 'approved' ? 'hidden' : '' }}" id="tab_approved">
  @include('pages.review-content.tabs.approved')
  </div>
  @endif

  @if($canViewPublished)
  <div class="transition-opacity duration-700 {{ $activeTab != 'published' ? 'hidden' : '' }}" id="tab_published">
  @include('pages.review-content.tabs.published')
  </div>
  @endif
@else
  <div class="container-fixed max-h-fit">
    <div class="flex items-center justify-center p-8">
      <div class="text-center">
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Access</h3>
        <p class="text-gray-600">You don't have permission to view review content.</p>
      </div>
    </div>
  </div>
@endif
{{-- End of review tabs --}}
@endsection

@push('scripts')
  <script>
    // Function to open document preview
    function openPreview(mediaType, id, filePath) {
      if (mediaType === 'Document') {
        // For documents, use our document viewer
        window.open('/review-content/view-document/' + id, '_blank');
      } else {
        // For other media types, just open the file directly
        window.open('{{ Storage::url("") }}/' + filePath, '_blank');
      }
    }
    const tabsEl = document.querySelector('#my_tabs');
    const options = {
      hiddenClass: 'hidden'
    };
    const tabs = new KTTabs(tabsEl, options);

    const keys = ["draft", "pending", "reviewed", "published", "approved", "rejected"];
    const pageTitle = $("#page-title");
    const selectionBanner = $("#banner-container");
    const smScreen = window.matchMedia("(max-width: 991px)").matches;
    const btnUpdate = $("#group-btn-approve");
    const btnPublish = $("#group-btn-publish");
    const btnLoading = $(".btn-loading");
    const showContent = `@include('pages.review-content.show')`;
    const emptyContent = `@include('pages.review-content.empty')`;
    const multipleContent = `@include('pages.review-content.multiple')`;

    // Check if user has main view permission
    @if(!$canViewAll)
    // No view permission, don't initialize anything
    let status = "";
    let filter = "";
    let flag = "";
    let detailContent = null;

    // Exit early if no permissions
    $(document).ready(function() {
      console.log("User does not have view permission for Review Content");
    });
    @else
    // Set initial status based on URL parameter or available permissions
    let status = "{{ $status }}";
    let filter = "index";
    let flag = "Reviewed";
    let detailContent = status ? $("#detail-"+status.toLowerCase()) : null;

    // Find and activate the correct tab based on status
    const tabToActivate = document.querySelector(`[data-status="${status}"]`);
    if (tabToActivate) {
      tabs.show(tabToActivate);
    }
    @endif

    // Adjust banner position based on sidebar width
    function adjustBannerPosition() {
      const sidebarWidth = $(".app-sidebar").width() || 280;
      const windowWidth = $(window).width();

      if (windowWidth >= 992) { // Desktop view
        // Set the left position to match the sidebar width exactly
        selectionBanner.css("left", sidebarWidth + "px");

        // Set the width to fill the remaining space
        selectionBanner.css("width", (windowWidth - sidebarWidth) + "px");
      } else { // Mobile view
        // Full width on mobile
        selectionBanner.css("left", "0");
        selectionBanner.css("width", "100%");
      }

      // Ensure the banner has proper z-index
      selectionBanner.css("z-index", "9999");
    }

    const contentCounts = {
      draft: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      },
      pending: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      },
      reviewed: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      },
      approved: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      },
      rejected: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      },
      published: {
        index: 0,
        image: 0,
        video: 0,
        audio: 0,
        document: 0,
      }
    };

    function getContent(status, mediaType, author, url) {
      $.ajax({
        url: url || "{{ route('get-content') }}",
        type: "GET",
        data: {
            'status': status,
            'media_type': mediaType
        },
        beforeSend: function() {
          $(".loading-content").show();
          $(".toolbar-skeleton").show();
          $("#filter-menu").hide();
          $("#tab-note").hide();
          $("#flag-menu").hide();
        },
        success: function(response) {
          let count = response.data.count;

          // Log the count data for debugging
          console.log("Count data:", count);

          // Update counts for all statuses
          keys.forEach(key => {
            // Convert to lowercase for consistency
            const statusKey = key.toLowerCase();

            // Get the count values, defaulting to 0 if not present
            contentCounts[statusKey].index = parseInt(count[statusKey] || 0);
            contentCounts[statusKey].image = parseInt(count[`${statusKey}_image`] || 0);
            contentCounts[statusKey].video = parseInt(count[`${statusKey}_video`] || 0);
            contentCounts[statusKey].audio = parseInt(count[`${statusKey}_audio`] || 0);
            contentCounts[statusKey].document = parseInt(count[`${statusKey}_document`] || 0);

            console.log(`Status: ${statusKey}, Count: ${contentCounts[statusKey].index}, Raw value:`, count[statusKey]);
          });

          // Special case: Set approved count to be the same as published count
          // This is because all approved content is immediately published
          contentCounts.approved.index = contentCounts.published.index;
          contentCounts.approved.image = contentCounts.published.image;
          contentCounts.approved.video = contentCounts.published.video;
          contentCounts.approved.audio = contentCounts.published.audio;
          contentCounts.approved.document = contentCounts.published.document;

          console.log("After adjustment - Approved count:", contentCounts.approved.index);

          $("#tab-note").show();
          $("#filter-menu").show();
          $(".toolbar-skeleton").hide();
          $(".loading-content").hide();
          contentData(response.data.content);

          // Always update filter for all statuses to ensure counts are displayed
          updateFilter(filter, status);

          if(["Reviewed"].includes(status)){
            $("#flag-menu").show();
            updateFlag(status, filter);
          }
        },
        error: function(status, error) {
            console.log("Error:", status, error);
        }
      });

    }

    @if($canViewAll)
    // Only try to get content if we have permissions
    if (status) {
      // Set initial badge class based on status
      let initialBadgeClass = 'badge-dark';
      if (status.toLowerCase() === 'pending') initialBadgeClass = 'badge-warning';
      else if (status.toLowerCase() === 'rejected') initialBadgeClass = 'badge-danger';
      else if (status.toLowerCase() === 'approved') initialBadgeClass = 'badge-primary';
      else if (status.toLowerCase() === 'published') initialBadgeClass = 'badge-success';

      $('#page-title-badge').removeClass('badge-dark badge-warning badge-danger badge-primary badge-success').addClass(initialBadgeClass);

      // Always update filter first to ensure counts are displayed correctly
      updateFilter(filter, status);

      // Then get content
      getContent(status, filter);
    }
    @endif

    function updateFilter(filter, status) {
      let filterMap = {
          index: { name: "Semua", icon: "fa-list" },
          image: { name: "Image", icon: "fa-image" },
          video: { name: "Video", icon: "fa-video" },
          audio: { name: "Audio", icon: "fa-music" },
          document: { name: "Document", icon: "fa-file" },
      };

      let pageNote = {
        draft: { note: "Have Not Been Submitted"},
        pending: { note: "Waiting To Be Reviewed"},
        reviewed: { note: "Have Been Reviewed"},
        rejected: { note: "Have Been Rejected"},
        approved: { note: "Have Been Approved"},
        published: { note: "Are Published"}
      }

      let statusKey = status.toLowerCase();
      let filterKey = filter.toLowerCase();

      // Make sure contentCounts[statusKey] exists
      if (!contentCounts[statusKey]) {
        contentCounts[statusKey] = { index: 0, image: 0, video: 0, audio: 0, document: 0 };
      }

      let count = contentCounts[statusKey][filterKey] ?? 0;

      // Make sure the filter menu is visible
      $("#filter-menu").show();

      // Update the filter button text
      $("#btn-filter").html(`
        <span class="flex items-center me-1">
         <i class="fa-solid ${filterMap[filterKey].icon}"></i>
        </span>
        <span class="hidden md:inline text-nowrap">
         ${filterMap[filterKey].name} (${count})
        </span>
        <span class="inline md:hidden text-nowrap">
         ${filterMap[filterKey].name} (${count})
        </span>
        <span class="flex items-center lg:ms-4">
         <i class="ki-filled ki-down !text-xs"></i>
        </span>
      `);

      // Update all filter menu items
      ["index", "image", "video", "audio", "document"].forEach(media => {
        let mediaCount = contentCounts[statusKey][media] ?? "0";
        $(`#filter-item-${media}`).text(`${filterMap[media].name} (${mediaCount})`);
      });

      // Update all tab counts
      keys.forEach(key => {
        // Always show the count
        const count = parseInt(contentCounts[key].index) || 0;
        $(`#tab-item-${key}`).text(count);
      });

      // Update the tab note
      $('#tab-note').text(contentCounts[statusKey].index+" File(s) "+pageNote[statusKey].note);
      $('#tab-note').show();

      // Update the page title badge count - always show the count
      const badgeCount = parseInt(contentCounts[statusKey].index) || 0;
      $('#page-title-badge').text(badgeCount);

      // Make sure the badge is visible
      $('#page-title-badge').removeClass('hidden');

      // Set the badge color based on status
      let badgeClass = 'badge-dark';
      if (statusKey === 'pending') badgeClass = 'badge-warning';
      else if (statusKey === 'rejected') badgeClass = 'badge-danger';
      else if (statusKey === 'approved') badgeClass = 'badge-primary';
      else if (statusKey === 'published') badgeClass = 'badge-success';

      $('#page-title-badge').removeClass('badge-dark badge-warning badge-danger badge-primary badge-success').addClass(badgeClass);
    }

    function updateFlag(flag, filter){
      let flagMap = {
          reviewed: { name: "Semua",},
          approved: { name: "Approved",},
          rejected: { name: "Rejected",}
      };

      let flagKey = flag.toLowerCase();
      let filterKey = filter.toLowerCase();

      // Make sure contentCounts[flagKey] exists
      if (!contentCounts[flagKey]) {
        contentCounts[flagKey] = { index: 0, image: 0, video: 0, audio: 0, document: 0 };
      }

      let count = contentCounts[flagKey][filterKey] ?? 0;

      // Make sure the flag menu is visible
      $("#flag-menu").show();

      // Update the flag button text
      $("#btn-flag").html(`
        <span class="hidden md:inline text-nowrap">
          ${flagMap[flagKey].name} (${count})
        </span>
        <span class="inline md:hidden text-nowrap">
          ${flagMap[flagKey].name} (${count})
        </span>
        <span class="flex items-center lg:ms-4">
          <i class="ki-filled ki-down !text-xs"></i>
        </span>
      `);

      // Update all flag menu items
      ["reviewed", "approved", "rejected"].forEach(media => {
        let mediaCount = contentCounts[media][filterKey] ?? "0";
        $(`#flag-item-${media}`).text(`${flagMap[media].name} (${mediaCount})`);
      });
    }

    @if($canViewAll)
    // Handle tab clicks (hidden but still functional)
    $(document).on("click", ".tab", function(){
      status = $(this).data("status");
      if (detailContent) detailContent.html(emptyContent);
      detailContent = $("#detail-"+status.toLowerCase());
      selectedIds = [];
      btnUpdate.addClass("hidden").removeClass("flex");
      btnPublish.addClass("hidden").removeClass("flex");

      // Update the page title tab name
      $('#page-title-tab').text(status);

      // Make sure filter and tab note are visible
      $("#filter-menu").show();
      $("#tab-note").show();

      // First update filter to ensure counts are displayed correctly
      updateFilter(filter, status);

      // Then get content
      getContent(status, filter);

      // Update URL without reloading the page
      const statusUrlMap = {
        'Pending': 'pending',
        'Rejected': 'rejected',
        'Approved': 'approved',
        'Published': 'published'
      };

      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set('tab', statusUrlMap[status]);
      window.history.pushState({}, '', newUrl);

      // Update sidebar menu active state
      $("#review-content-submenu .menu-link").removeClass("bg-gray-50 dark:bg-coal-300");
      $(`#review-content-submenu a[href*="tab=${statusUrlMap[status]}"]`).addClass("bg-gray-50 dark:bg-coal-300");
    });

    // Handle sidebar navigation clicks
    $('#review-content-submenu a').on('click', function(e) {
      // Only handle clicks if we're already on the review-content page
      if (window.location.pathname.includes('/review-content')) {
        e.preventDefault();

        // Get the tab parameter from the URL
        const href = $(this).attr('href');
        const url = new URL(href, window.location.origin);
        const tabParam = url.searchParams.get('tab');

        // Map URL parameter to status
        const tabStatusMap = {
          'pending': 'Pending',
          'rejected': 'Rejected',
          'approved': 'Approved',
          'published': 'Published'
        };

        // Find and click the corresponding tab
        if (tabParam && tabStatusMap[tabParam]) {
          const tabToClick = document.querySelector(`[data-status="${tabStatusMap[tabParam]}"]`);
          if (tabToClick) {
            // Update the page title tab name directly
            $('#page-title-tab').text(tabStatusMap[tabParam]);

            // Make sure filter and tab note are visible
            $("#filter-menu").show();
            $("#tab-note").show();

            // Update status
            status = tabStatusMap[tabParam];

            // First update filter to ensure counts are displayed correctly
            updateFilter(filter, status);

            // Then click the tab to update everything else
            tabToClick.click();
          }
        }
      }
    });

    $(document).on("click", ".filter-media", function(){
      filter = $(this).data("filter");
      $(".filter-media").removeClass("active");
      $(this).addClass("active");

      // Make sure filter and tab note are visible
      $("#filter-menu").show();
      $("#tab-note").show();

      // First update filter to ensure counts are displayed correctly
      updateFilter(filter, status);

      // Then get content
      getContent(status, filter);
    });

    $(document).on("click", ".flag", function(){
      flag = $(this).data("flag");
      $(".flag").removeClass("active");
      $(this).addClass("active");

      // Make sure flag menu is visible
      $("#flag-menu").show();

      // First update flag to ensure counts are displayed correctly
      updateFlag(flag, filter);

      // Then get content
      getContent(flag, filter);
    });

    // Add window resize event to adjust banner position
    $(window).on('resize', function() {
      if (selectionBanner.is(":visible")) {
        adjustBannerPosition();
      }
      smScreen = window.matchMedia("(max-width: 991px)").matches;
    });

    // Initialize banner position
    adjustBannerPosition();

    // Add custom CSS to ensure dropdowns are visible
    if (!$("#dropdown-fix").length) {
      $("head").append(`
        <style id="dropdown-fix">
          .dropdown-content {
            z-index: 9999 !important;
            position: absolute !important;
          }
        </style>
      `);
    }
    @endif

    @if($canViewAll)
    function contentData(data) {
      if (!status) return; // Skip if no status (no permissions)

      let container = $("#content-loader-"+status.toLowerCase());
      let itemData = {};
      container.empty();
      selectionBanner.hide();

      // Make sure filter and tab note are visible
      $("#filter-menu").show();
      $("#tab-note").show();

      data.data.forEach(item => {
          itemData[item.id] = item;

          let mediaElement = item.media_type == "Document" || item.media_type == "Audio" ? `{{ asset('assets/${item.thumbnail_path}') }}` : `{{ Storage::url('${item.thumbnail_path}') }}`;
          let checkboxElement = "";
          let deleteElement = "";
          let badge = '';

          if(item.status === "Published"){
            checkboxElement = `
              <div class="multiple-select z-10 bg-neutral-600 hover:bg-neutral-800 flex absolute top-1 left-1 justify-center items-center rounded-full w-7 h-7 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div class="w-4 h-4 rounded-sm bg-transparent border-white border-2 unchecked"></div>
                <i class="hidden ki-filled ki-check text-lg text-white checked"></i>
              </div>
            `;
          }
          if(item.status == "Draft"){
            deleteElement = `
              <div class="menu-item" data-dropdown-dismiss="true">
                <button class="menu-link btn-delete" data-id="${item.id}">
                  <span class="menu-icon">
                      <i class="ki-filled ki-trash"></i>
                  </span>
                  <span class="menu-title">Delete</span>
                </button>
              </div>
            `;
          }

          // Determine badge color based on status
          switch(item.status){
            case "Draft": badge = 'light'; break;
            case "Pending": badge = 'warning'; break;
            case "Rejected": badge = 'danger'; break;
            case "Approved": badge = 'primary'; break;
            case "Published": badge = 'success'; break;
          }

          // Override display status for the Approved tab
          let displayStatus = item.status;
          if (status === "Approved" && item.status === "Published") {
            displayStatus = "Approved";
            badge = 'primary';
          }

          let card = `
          <div class="relative">
            <div class="card group bg-[--tw-navbar-bg] border border-gray-300 cursor-pointer select-card" data-id="${item.id}">
              ${checkboxElement}
              <div class="group/content relative w-full aspect-square p-0 flex justify-center items-center overflow-hidden">
                <img alt="" class="w-full h-full object-cover group-hover/content:scale-90 transition-transform duration-300 ease-out" src="${mediaElement}"/>
              </div>
              <div class="bg-white border-t-2 card-rounded-b flex flex-col px-3 py-1.5">
                <span class="text-xs font-medium text-gray-900 truncate">${item.original_filename || item.filename}</span>
                <div class="flex items-center justify-between grow">
                    <div class="flex items-center grow">
                        <span class="my-0.5 badge badge-sm badge-outline badge-${badge}">${displayStatus}</span>
                    </div>
                </div>
              </div>
            </div>
            <div class="dropdown absolute bottom-1 right-1 z-40" data-dropdown="true" data-dropdown-placement="left-start">
                <button class="dropdown-toggle btn btn-xs btn-icon btn-light hover:bg-neutral-100 rounded-full border-0">
                    <i class="ki-filled ki-dots-vertical"></i>
                </button>
                <div class="dropdown-content menu-default w-[220px] z-50">
                    <div class="menu-item" data-dropdown-dismiss="true">
                        <a onclick="openPreview('${item.media_type}', '${item.id}', '${item.file_path}')" href="javascript:void(0)" class="menu-link">
                            <span class="menu-icon">
                                <i class="ki-filled ki-exit-right-corner"></i>
                            </span>
                            <span class="menu-title">See large preview</span>
                        </a>
                    </div>
                    ${deleteElement}
                </div>
            </div>
          </div>`;

          container.append(card);
      });

      let $paginationContainer = $("#pagination-container");
      $paginationContainer.empty();

      if (data.links) {
          let active = `class="z-10 page-link flex items-center justify-center px-3 h-8 leading-tight text-blue-600 border border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white"`;
          let inactive = `class="page-link flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"`;

          data.links.forEach(link => {
            let navigation = link.label;
            let pageLink;

            if(navigation.toLowerCase().includes("previous")){
              pageLink = `
                <li>
                  <a href="#" data-url="${link.url}" class="page-link flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-e-0 border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <span class="sr-only">${link.label}</span>
                    <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                    </svg>
                  </a>
                </li>
              `;
            }else if(navigation.toLowerCase().includes("next")){
              pageLink = `
                <li>
                  <a href="#" data-url="${link.url}" class="page-link flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    <span class="sr-only">${link.label}</span>
                    <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>
                  </a>
                </li>
              `;
            }else{
              pageLink = `
                <li>
                  <a href="#" data-url="${link.url}" ${link.active ? active+' aria-current="page"' : inactive}>${link.label}</a>
                </li>
              `;
            }

            $paginationContainer.append(pageLink);
          });

          $(".page-link").on("click", function (e) {
              e.preventDefault();
              let url = $(this).data("url");
              detailContent.html(emptyContent);
              getContent(status, filter, url);
          });
      }

      let selectedIds = [];
      let selectedKeywords = [];
      let isMultipleSelect = false;

      // ===== KEYWORD HANDLING - COMPLETELY REWRITTEN =====

      // Update the hidden input with the current keywords
      function updateHiddenInput() {
        $("#hiddenKeywords").val(selectedKeywords.join(','));
      }

      // Update the UI based on the current keyword count
      function updateKeywordUI() {
        // Update the counter
        $("#keywords_counter").text(selectedKeywords.length);
      }

      // Add a single keyword to the list
      function addKeyword(keyword) {
        // Skip if empty or already exists
        keyword = keyword.trim();
        if (keyword === '' || selectedKeywords.includes(keyword)) {
          return;
        }

        // Add to array
        selectedKeywords.push(keyword);

        // Create visual tag
        let container = (status === "Draft") ? $("#keywordsContainerDraft") : $("#keywordsContainerShow");
        let tag;

        if (status === "Draft") {
          tag = `
            <span class="keyword-item px-3 py-2 bg-neutral-200 text-xs rounded-full flex items-center">
                ${keyword}
                <button type="button" class="ml-2 text-white remove-keyword" data-keyword="${keyword}">
                  <i class="ki-filled ki-cross text-gray-700"></i>
                </button>
            </span>
          `;
        } else {
          tag = `
            <span class="keyword-item px-3 py-2 bg-neutral-200 text-xs rounded-full flex items-center">
                ${keyword}
            </span>
          `;
        }

        container.append(tag);

        // Update UI and hidden input
        updateHiddenInput();
        updateKeywordUI();
      }

      // Process multiple keywords (comma-separated)
      function processKeywords(input) {
        if (!input || input.trim() === '') {
          return;
        }

        // Split by comma, trim each part, and filter out empty strings
        let keywords = input.split(',')
          .map(k => k.trim())
          .filter(k => k.length > 0);

        // Add each keyword
        keywords.forEach(keyword => addKeyword(keyword));
      }

      // Load keywords for an existing content
      function loadKeywords(contentId) {
        $.ajax({
          url: `submit-content/get-keywords/${contentId}`,
          method: "GET",
          success: function (response) {
            // Clear existing keywords
            selectedKeywords = [];
            let container = (status === "Draft") ? $("#keywordsContainerDraft") : $("#keywordsContainerShow");
            container.empty();

            // Add each keyword from the response
            response.data.forEach(keyword => {
              addKeyword(keyword);
            });
          }
        });
      }

      // EVENT HANDLERS

      // Remove keyword when clicking the X button
      $(document).on("click", ".remove-keyword", function () {
        let keyword = $(this).data("keyword");

        // Remove from array
        selectedKeywords = selectedKeywords.filter(k => k !== keyword);

        // Remove visual tag
        $(this).parent().remove();

        // Update UI and hidden input
        updateHiddenInput();
        updateKeywordUI();
      });

      // Update counter as user types
      $(document).on("keyup", "#keywordInput", function () {
        let inputValue = $(this).val().trim();
        let pendingCount = 0;

        if (inputValue.length > 0) {
          // Count comma-separated keywords in the input field
          pendingCount = inputValue.split(",")
            .map(k => k.trim())
            .filter(k => k.length > 0).length;
        }

        // Show total (existing + pending)
        $("#keywords_counter").text(selectedKeywords.length + pendingCount);
      });

      // Handle Enter key to add keywords
      $(document).on("keydown", "#keywordInput", function (e) {
        if (e.key === "Enter") {
          e.preventDefault();
          let input = $(this).val().trim();

          if (input.length > 0) {
            // Process the input as keywords
            processKeywords(input);

            // Clear the input field
            $(this).val("");
          }
        }
      });

      // Autofill suggestions completely disabled

      function updateSelection(card, cardId, isChecked) {
        let checkbox = card.find(".multiple-select");
        let uncheckedIcon = card.find(".unchecked");
        let checkedIcon = card.find(".checked");

        if (isChecked) {
            if (!selectedIds.includes(cardId)) {
                selectedIds.push(cardId);
            }
            card.addClass("border-blue-500").removeClass("border-gray-300");

            // Hanya ubah checkbox jika dalam mode multiple-select
            if (isMultipleSelect) {
                checkbox.removeClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100").addClass("bg-neutral-900 opacity-100");
                uncheckedIcon.hide();
                checkedIcon.show();
                selectionBanner.show();
                if(smScreen) pageTitle.hide();
                $('#banner-title').text(selectedIds.length+" item dipilih");
            }
        } else {
            selectedIds = selectedIds.filter(id => id !== cardId);
            card.removeClass("border-blue-500").addClass("border-gray-300");

            // Hanya ubah checkbox jika dalam mode multiple-select
            if (isMultipleSelect) {
                checkbox.removeClass("bg-neutral-900 opacity-100").addClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100");
                uncheckedIcon.show();
                checkedIcon.hide();
                $('#banner-title').text(selectedIds.length+" item dipilih");
            }

            // Jika tidak ada card yang dipilih, kembali ke mode single-select
            if (selectedIds.length === 0) {
                isMultipleSelect = false;
                selectionBanner.hide();
                pageTitle.show();
            }
        }
        updateShow();
      }

      function updateShow(){
        let cardId = selectedIds[0];
        let cardData = itemData[cardId];
        let statusKey = status.toLowerCase();
        let storagePath = "{{ asset('storage/') }}";

        detailContent.html(selectedIds.length === 0 ? emptyContent : (selectedIds.length === 1 ? showContent : multipleContent));

        if (selectedIds.length == 1) {
          $("#filename").text(cardData.filename ?? '-');
          $("#description").text(cardData.description ?? '-');
          $("#category").text(cardData.category.name ?? '-');
          if(cardData.category_2 != null) $("#category_2").text(cardData.category_2.name);
          $("#media_type").text(cardData.media_type ?? '-');
          $("#creator_name").text(cardData.creator_name ?? '-');
          if(cardData.date_taken != null) $("#date_taken").text(cardData.date_taken.split(" ")[0]);
          if(cardData.release_document != null) $("#current_release_doc").html(`File saat ini: <a href="${storagePath}/${cardData.release_document}" target="_blank" class="text-blue-500 cursor-pointer">Lihat File</a>`);
          loadKeywords(cardId);
          if(status == "Pending") btnUpdate.removeClass("hidden").addClass("flex");
          cardState = cardData.status
          // Only show publish button for content that is not Approved, Rejected, or Published
          // Approved content is now automatically published
          if(cardState != "Rejected" && cardState != "Approved" && cardState != "Published") btnPublish.removeClass("hidden").addClass("flex");
          $("#btn-approve").data("id", cardId);
          $("#btn-reject").data("id", cardId);
          $("#btn-publish").data("id", cardId);
          $("#btn-refuse").data("id", cardId);

          $("#accordion_item").html(`
            <button class="accordion-toggle pb-2 group btn-review-history" data-id="${cardId}">
              <span class="">
              Riwayat Penilaian
              </span>
              <i class="ki-outline ki-down text-gray-600 text-2sm" id="plus-accordion-${cardId}">
              </i>
              <i class="ki-outline ki-up text-gray-600 text-2sm hidden" id="minus-accordion-${cardId}">
              </i>
            </button>
            <div class="text-center loading hidden mt-2">
              <div role="status">
                <svg aria-hidden="true" class="inline w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-gray-600 dark:fill-gray-300" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                    <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
                </svg>
                <span class="sr-only">Loading...</span>
              </div>
            </div>
            <div class="accordion-content hidden" id="accordion-${cardId}"></div>
          `);

        }else if(selectedIds.length == 0){
          if(status == "Pending") btnUpdate.addClass("hidden").removeClass("flex");
          if(status == "Reviewed" || status == "Approved" || status == "Rejected") btnPublish.addClass("hidden").removeClass("flex");
        }
        selectedKeywords = [];
        $("#keywordsContainerShow").empty();
      }

      // Event: Klik pada card (single select mode atau multiple select)
      $(".select-card").on("click", function (e) {
          let card = $(this);
          let cardId = card.data("id");

          if (!isMultipleSelect) {
              // Toggle single-select mode tanpa mengubah checkbox
              if (selectedIds.includes(cardId)) {
                  updateSelection(card, cardId, false);
              } else {
                  $(".select-card").each(function () {
                      updateSelection($(this), $(this).data("id"), false);
                  });
                  updateSelection(card, cardId, true);
              }
          } else {
              // Jika dalam mode multiple-select, tambahkan atau hapus dari daftar
              let isSelected = selectedIds.includes(cardId);
              updateSelection(card, cardId, !isSelected);
          }
          console.log("Selected IDs:", selectedIds);
      });

      // Event: Klik pada checkbox multiple-select
      $(".multiple-select").on("click", function (e) {
          e.stopPropagation(); // Hindari trigger klik pada card

          let card = $(this).closest(".select-card");
          let cardId = card.data("id");
          let cardData = itemData[cardId];

          if (!isMultipleSelect) {
              isMultipleSelect = true;
              if (!selectedIds.includes(cardId)) {
                  selectedIds.push(cardId);
              }
              updateSelection(card, cardId, true);
          } else {
              // Jika sudah dalam mode multiple-select, toggle pemilihan card
              let isSelected = selectedIds.includes(cardId);
              updateSelection(card, cardId, !isSelected);
          }

          console.log("Multiple Select Status:", isMultipleSelect, "Selected IDs:", selectedIds);
      });

      $(document).off("click", ".btn-review-history").on("click", ".btn-review-history", function (e) {
        let cardId = $(this).data("id");
        let accordionContent = $("#accordion-"+cardId);
        let loading = $(".loading");
        accordionContent.empty()

        if(accordionContent[0].classList.length == 2){
          accordionContent.removeClass("hidden")
          $("#plus-accordion-"+cardId).addClass("hidden")
          $("#minus-accordion-"+cardId).removeClass("hidden")
          $.ajax({
            url: "review-content/get-reviews/"+cardId,
            method: "GET",
            beforeSend: function() {
              loading.show();
            },
            success: function(res) {
              let data = res.data;
              data.forEach(item => {
                let badgeStatus;

                switch (item.status){
                  case 'Approved': badgeStatus = 'badge-primary'; break;
                  case 'Refused': badgeStatus = 'badge-danger'; break;
                  case 'Publish': badgeStatus = 'badge-success'; break;
                }

                // Override display status for the Approved tab
                let reviewDisplayStatus = item.status;
                if (status === "Approved" && item.status === "Publish") {
                  reviewDisplayStatus = "Approved";
                  badgeStatus = 'badge-primary';
                }

                accordionContent.append(`
                  <div class="card p-4 mb-2 text-gray-700 text-md pb-4 relative">
                    <h6 class="font-bold">${item.reviewer.name}</h6>
                    <span class="text-xs">${item.review_date}</span>
                    <p class="text-sm mt-2">Note: ${item.comments ?? '-'}</p>
                    <span class="badge badge-outline ${badgeStatus} absolute top-3 right-3 rounded-full">${reviewDisplayStatus}</span>
                  </div>
                `);
              })
              loading.hide();
            },
            error: function(err) {
              let res = err.responseJSON;
            }
          });
        }else{
          accordionContent.addClass("hidden")
          $("#plus-accordion-"+cardId).removeClass("hidden")
          $("#minus-accordion-"+cardId).addClass("hidden")
          loading.hide();
        }
      });

      $(document).on("click", "#btn-approve", function (e) {
          e.preventDefault();
          let contentId = selectedIds;
          approveContent(contentId);
      });

      $(document).on("click", "#btn-reject", function (e) {
          e.preventDefault();
          let contentId = selectedIds;
          rejectContent(contentId);
      });

      $(document).on("click", "#btn-publish", function (e) {
          e.preventDefault();
          let contentId = selectedIds;
          publishContent(contentId);
      });

      $(document).on("click", "#btn-refuse", function (e) {
          e.preventDefault();
          let contentId = selectedIds;
          refuseContent(contentId);
      });

      // $(".btn-delete").on("click", function (e) {
      //     e.preventDefault();
      //     let contentId = $(this).data("id");
      //     deleteContent(contentId);
      // });

      $('#btn-bulk-delete').on('click', function(e){
        e.preventDefault();
        if (selectedIds.length > 0) {
          Swal.fire({
            title: "Are you sure?",
            text: "You want to delete " + selectedIds.length + " selected items?",
            icon: "warning",
            showCancelButton: true,
            customClass:{
              confirmButton: 'btn btn-light',
              cancelButton: 'btn btn-danger',
            },
            confirmButtonText: "Yes, delete them!"
          }).then((result) => {
            if (result.isConfirmed) {
              // Call your delete function here
              // bulkDelete(selectedIds);
              console.log("Would delete:", selectedIds);

              // For now just show a message
              myToast("success", "Bulk delete functionality would be implemented here");

              // Reset selection
              selectedIds = [];
              isMultipleSelect = false;
              selectionBanner.hide();
              if(smScreen) pageTitle.show();

              // Refresh content
              getContent(status, filter);
            }
          });
        }
      })

      $('#btn-close-banner').on('click', function(){
        $(".select-card").removeClass("border-blue-500").addClass("border-gray-300");
        $(".multiple-select").removeClass("bg-neutral-900").addClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100");
        $(".unchecked").show();
        $(".checked").hide();
        selectedIds = [];
        isMultipleSelect = false;
        selectionBanner.hide();
        resetContent();
      })
    }
    @endif

    @if($canViewAll)
    function approveContent(contentId) {
      Swal.fire({
        title: "Are you sure?",
        text: "Approved content will be automatically published. You won't be able to revert this!",
        inputLabel: "Catatan (opsional)",
        input: "textarea",
        inputPlaceholder: "Masukan catatan approval disini...",
        inputAttributes: {
          "aria-label": "Masukan catatan approval disini..."
        },
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, approve and publish it!"
      }).then((result) => {
        if (result.isConfirmed) {
          const comments = result.value
          $.ajax({
            url: "review-content/approval",
            method: 'PUT',
            data: {
              '_token': '{{ csrf_token() }}',
              'id': contentId,
              'type': 'approve',
              'comments': comments
            },
            beforeSend: function() {
              btnLoading.addClass("flex").removeClass("hidden");
              btnUpdate.removeClass("flex").addClass("hidden");
            },
            success: function(res){
              myToast(res.status, res.message)
              getContent(status, filter)
              resetContent()
              btnLoading.removeClass("flex").addClass("hidden");
            },
            error:function(err){
              let res = err.responseJSON
              myToast(res.status, res.message)
              getContent(status, filter)
              resetContent()
            }
          });
        }
      });
    }

    function rejectContent(contentId) {
      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        inputLabel: "Catatan (opsional)",
        input: "textarea",
        inputPlaceholder: "Masukan catatan approval disini...",
        inputAttributes: {
          "aria-label": "Masukan catatan approval disini..."
        },
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, reject it!"
      }).then((result) => {
        if (result.isConfirmed) {
          const comments = result.value
          $.ajax({
            url: "review-content/approval",
            method: 'PUT',
            data: {
              '_token': '{{ csrf_token() }}',
              'id': contentId,
              'type': 'reject',
              'comments': comments
            },
            beforeSend: function() {
              btnLoading.addClass("flex").removeClass("hidden");
              btnUpdate.removeClass("flex").addClass("hidden");
            },
            success: function(res){
              myToast(res.status, res.message)
              getContent(status, filter)
              resetContent()
              btnLoading.removeClass("flex").addClass("hidden");
            },
            error:function(err){
              let res = err.responseJSON
              myToast(res.status, res.message)
              getContent(status, filter)
              resetContent()
            }
          });
        }
      });
    }

    function publishContent(contentId) {
      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        inputLabel: "Catatan (opsional)",
        input: "textarea",
        inputPlaceholder: "Masukan catatan approval disini...",
        inputAttributes: {
          "aria-label": "Masukan catatan approval disini..."
        },
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, publish it!"
      }).then((result) => {
        if (result.isConfirmed) {
          const comments = result.value
          $.ajax({
            url: "review-content/publication",
            method: 'PUT',
            data: {
              '_token': '{{ csrf_token() }}',
              'id': contentId,
              'type': 'publish',
              'comments': comments
            },
            beforeSend: function() {
              btnLoading.addClass("flex").removeClass("hidden");
              btnPublish.removeClass("flex").addClass("hidden");
            },
            success: function(res){
              myToast(res.status, res.message)
              getContent(status, filter)
              resetContent()
              btnLoading.removeClass("flex").addClass("hidden");
            },
            error:function(err){
              let res = err.responseJSON
              myToast(res.status, res.message)
              getContent(status, filter)
              resetContent()
            }
          });
        }
      });
    }

    function refuseContent(contentId) {
      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        inputLabel: "Catatan (opsional)",
        input: "textarea",
        inputPlaceholder: "Masukan catatan approval disini...",
        inputAttributes: {
          "aria-label": "Masukan catatan approval disini..."
        },
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, refuse it!"
      }).then((result) => {
        if (result.isConfirmed) {
          const comments = result.value
          $.ajax({
            url: "review-content/publication",
            method: 'PUT',
            data: {
              '_token': '{{ csrf_token() }}',
              'id': contentId,
              'type': 'refuse',
              'comments': comments
            },
            beforeSend: function() {
              btnLoading.addClass("flex").removeClass("hidden");
              btnPublish.removeClass("flex").addClass("hidden");
            },
            success: function(res){
              myToast(res.status, res.message)
              getContent(status, filter)
              resetContent()
              btnLoading.removeClass("flex").addClass("hidden");
            },
            error:function(err){
              let res = err.responseJSON
              myToast(res.status, res.message)
              getContent(status, filter)
              resetContent()
            }
          });
        }
      });
    }

    function resetContent() {
      detailContent.html(emptyContent)
      if(status == "Pending") btnUpdate.addClass("hidden").removeClass("flex");
      // Hide publish button for all statuses
      btnPublish.addClass("hidden").removeClass("flex");
      selectedKeywords = [];
      $("#keywordsContainerShow").empty();
      $("#filename").text("");
      $("#description").text("");
      $("#category").text("");
      $("#category_2").text("");
      $("#media_type").text("");
      $("#creator_name").text("");
      $("#date_taken").text("");
      $("#current_release_doc").html("");
    }

    function deleteContent(contentId) {
      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, delete it!"
      }).then((result) => {
        if (result.isConfirmed) {
          $.ajax({
            url: "submit-content/"+contentId+"/delete",
            method: 'DELETE',
            data: {
              '_token': '{{ csrf_token() }}'
            },
            success: function(res){
              myToast(res.status, res.message)
              getContent(status, filter)
            },
            error:function(err){
              let res = err.responseJSON
              myToast(res.status, res.message)
              getContent(status, filter)
            }
          });
        }
      });
    }

    function bulkDelete(contentIds) {
      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, delete it!"
      }).then((result) => {
        if (result.isConfirmed) {
          $.ajax({
            url: "submit-content/bulk-delete",
            method: 'DELETE',
            data: {
              '_token': '{{ csrf_token() }}',
              'ids': contentIds
            },
            success: function(res){
              myToast(res.status, res.message)
              getContent(status, filter)
            },
            error:function(err){
              let res = err.responseJSON
              myToast(res.status, res.message)
              getContent(status, filter)
            }
          });
        }
      });
    }
    @endif

  </script>
@endpush