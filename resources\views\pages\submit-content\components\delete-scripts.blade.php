<script>
  // This script uses global variables defined in main-scripts.blade.php
  function deleteContent(contentId) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      customClass:{
        confirmButton: 'btn btn-light',
        cancelButton: 'btn btn-danger',
      },
      confirmButtonText: "Yes, delete it!"
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: "submit-content/"+contentId+"/delete",
          method: 'DELETE',
          data: {
            '_token': '{{ csrf_token() }}'
          },
          success: function(res){
            myToast(res.status, res.message)
            getContent(status, filter, authorId)
          },
          error:function(err){
            let res = err.responseJSON
            myToast(res.status, res.message)
            getContent(status, filter, authorId)
          }
        });
      }
    });
  }

  function bulkDelete(contentIds) {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      customClass:{
        confirmButton: 'btn btn-light',
        cancelButton: 'btn btn-danger',
      },
      confirmButtonText: "Yes, delete it!"
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: "submit-content/bulk-delete",
          method: 'DELETE',
          data: {
            '_token': '{{ csrf_token() }}',
            'ids': contentIds
          },
          success: function(res){
            myToast(res.status, res.message)
            getContent(status, filter, authorId)
            detailContent.html(emptyContent)
            btnUpdate.addClass("hidden").removeClass("flex");
            selectedIds = [];
          },
          error:function(err){
            let res = err.responseJSON
            myToast(res.status, res.message)
            getContent(status, filter, authorId)
          }
        });
      }
    });
  }
</script>
