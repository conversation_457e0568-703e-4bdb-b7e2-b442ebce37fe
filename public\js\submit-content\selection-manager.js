/**
 * Submit Content Selection Manager Module
 * Handles card selection and multiple selection logic
 */

window.SubmitContent = window.SubmitContent || {};

window.SubmitContent.SelectionManager = {
  
  /**
   * Handle card selection logic
   */
  handleCardSelection: function(card, cardId) {
    const state = window.SubmitContent.state;
    
    if (!state.isMultipleSelect) {
      // Toggle single-select mode tanpa mengubah checkbox
      if (state.selectedIds.includes(cardId)) {
        this.updateSelection(card, cardId, false);
      } else {
        $(".select-card").each(function () {
          window.SubmitContent.SelectionManager.updateSelection($(this), $(this).data("id"), false);
        });
        this.updateSelection(card, cardId, true);
      }
    } else {
      // Jika dalam mode multiple-select, tambahkan atau hapus dari daftar
      let isSelected = state.selectedIds.includes(cardId);
      this.updateSelection(card, cardId, !isSelected);

      // Hide content details when in multiple selection mode
      state.detailContent.html(state.emptyContent);
    }
  },

  /**
   * Handle showing content details
   */
  handleShowDetails: function(card, cardId) {
    const state = window.SubmitContent.state;
    
    // Highlight the card visually without changing checkbox state
    $(".select-card").removeClass("border-blue-500").addClass("border-gray-300");
    card.addClass("border-blue-500").removeClass("border-gray-300");

    // Update selectedIds for tracking which item is being viewed
    // but don't update checkbox visual state
    state.selectedIds = [cardId];

    // Tampilkan detail content
    state.status == "Draft" ? updateForm() : updateShow();
  },

  /**
   * Update selection state for a card
   */
  updateSelection: function(card, cardId, isChecked) {
    const state = window.SubmitContent.state;
    let checkbox = card.find(".multiple-select");
    let uncheckedIcon = card.find(".unchecked");
    let checkedIcon = card.find(".checked");

    if (isChecked) {
      if (!state.selectedIds.includes(cardId)) {
        state.selectedIds.push(cardId);
      }
      card.addClass("border-blue-500").removeClass("border-gray-300");

      // Only update checkbox visual state when in multiple select mode
      // or when the checkbox itself was clicked
      if (state.isMultipleSelect) {
        checkbox.removeClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100").addClass("bg-neutral-900 opacity-100");
        uncheckedIcon.hide();
        checkedIcon.show();

        // Adjust banner position before showing it
        window.SubmitContent.UIManager.adjustBannerPosition();
        state.selectionBanner.show();
        if(state.smScreen) state.pageTitle.hide();
        $('#banner-title').text(state.selectedIds.length+" item dipilih");
      }
    } else {
      state.selectedIds = state.selectedIds.filter(id => id !== cardId);
      card.removeClass("border-blue-500").addClass("border-gray-300");

      // Only update checkbox visual state when in multiple select mode
      // or when the checkbox itself was clicked
      if (state.isMultipleSelect) {
        checkbox.removeClass("bg-neutral-900 opacity-100").addClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100");
        uncheckedIcon.show();
        checkedIcon.hide();
        $('#banner-title').text(state.selectedIds.length+" item dipilih");
      }

      // Jika tidak ada card yang dipilih, kembali ke mode single-select
      if (state.selectedIds.length === 0) {
        state.isMultipleSelect = false;
        state.selectionBanner.hide();
        state.pageTitle.show();
      }
    }

    // Don't automatically update the form/show content here
    // This will be handled by the specific handlers
  },

  /**
   * Handle checkbox click for multiple selection
   */
  handleCheckboxClick: function(element) {
    const state = window.SubmitContent.state;
    let card = $(element).closest(".select-card");
    let cardId = card.data("id");
    let isSelected = state.selectedIds.includes(cardId);

    // Always enter multiple selection mode when clicking on a checkbox
    state.isMultipleSelect = true;

    // Toggle selection state
    if (isSelected) {
      // Deselect
      state.selectedIds = state.selectedIds.filter(id => id !== cardId);
      card.removeClass("border-blue-500").addClass("border-gray-300");

      let checkbox = card.find(".multiple-select");
      let uncheckedIcon = card.find(".unchecked");
      let checkedIcon = card.find(".checked");

      checkbox.removeClass("bg-neutral-900 opacity-100").addClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100");
      uncheckedIcon.show();
      checkedIcon.hide();
    } else {
      // Select
      state.selectedIds.push(cardId);
      card.addClass("border-blue-500").removeClass("border-gray-300");

      let checkbox = card.find(".multiple-select");
      let uncheckedIcon = card.find(".unchecked");
      let checkedIcon = card.find(".checked");

      checkbox.removeClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100").addClass("bg-neutral-900 opacity-100");
      uncheckedIcon.hide();
      checkedIcon.show();
    }

    // Update banner
    // Adjust banner position before showing it
    window.SubmitContent.UIManager.adjustBannerPosition();
    state.selectionBanner.show();
    if(state.smScreen) state.pageTitle.hide();
    $('#banner-title').text(state.selectedIds.length+" item dipilih");

    // If no items selected, exit multiple selection mode
    if (state.selectedIds.length === 0) {
      state.isMultipleSelect = false;
      state.selectionBanner.hide();
      if(state.smScreen) state.pageTitle.show();
    }

    // Hide content details when in multiple selection mode
    if (state.isMultipleSelect) {
      state.detailContent.html(state.emptyContent);
    }
  },

  /**
   * Clear all selections and exit multiple selection mode
   */
  clearSelections: function() {
    const state = window.SubmitContent.state;
    
    $(".select-card").removeClass("border-blue-500").addClass("border-gray-300");
    $(".multiple-select").removeClass("bg-neutral-900 opacity-100").addClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100");
    $(".unchecked").show();
    $(".checked").hide();
    state.selectedIds = [];
    state.isMultipleSelect = false;
    state.selectionBanner.hide();
    if(state.smScreen) state.pageTitle.show();
    state.detailContent.html(state.emptyContent);
  }
};
