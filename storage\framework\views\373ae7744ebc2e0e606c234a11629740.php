<!-- Include modular JavaScript files -->
<script src="<?php echo e(asset('js/submit-content/config.js')); ?>"></script>
<script src="<?php echo e(asset('js/submit-content/content-manager.js')); ?>"></script>
<script src="<?php echo e(asset('js/submit-content/ui-manager.js')); ?>"></script>
<script src="<?php echo e(asset('js/submit-content/selection-manager.js')); ?>"></script>
<script src="<?php echo e(asset('js/submit-content/event-handlers.js')); ?>"></script>
<script src="<?php echo e(asset('js/submit-content/main.js')); ?>"></script>

<script>
  // Pass Laravel data to JavaScript
  window.SubmitContentConfig = window.SubmitContentConfig || {};
  window.SubmitContentConfig.authorId = "<?php echo e(auth()->user()->id); ?>";
  window.SubmitContentConfig.routes = {
    getContent: "<?php echo e(route('get-content-author')); ?>"
  };
  window.SubmitContentConfig.templates = {
    formContent: `<?php echo $__env->make('pages.submit-content.form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>`,
    showContent: `<?php echo $__env->make('pages.submit-content.show', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>`,
    emptyContent: `<?php echo $__env->make('pages.submit-content.empty', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>`
  };
</script><?php /**PATH C:\laragon\www\grafindo-media-library\resources\views/pages/submit-content/components/main-scripts.blade.php ENDPATH**/ ?>