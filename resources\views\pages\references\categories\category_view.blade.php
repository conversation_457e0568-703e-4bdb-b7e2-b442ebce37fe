@extends('layouts.partials.main')

@section('title', 'Categories')

@section('content')

{{-- Head Content --}}
<div class="mb-5 lg:mb-7.5">
  <!-- Container -->
  <div class="container-fixed flex items-center justify-between flex-wrap gap-5">
   <div class="flex flex-col justify-center items-start flex-wrap gap-1 lg:gap-2">
    <h1 class="font-medium text-lg text-gray-900">
     {{ @$page }}
    </h1>
    <div class="flex items-center gap-1 text-sm font-normal">
     <a class="text-gray-700">References</a>
     <span class="text-gray-400 text-sm"> / </span>
     <a class="text-gray-700 hover:text-primary" href="{{ route('categories') }}">Categories</a>
    </div>
   </div>
   @canAccess('add.category1', 'add.category2')
   <div class="flex items-center flex-wrap gap-1.5 lg:gap-3.5">
    <a id="create-category" class="btn btn-light" href="{{ route('categories-create') }}">
     <i class="fa-solid fa-plus">
     </i>
     Create category
    </a>
   </div>
   @endcanAccess
  </div>
  <!-- End of Container -->
</div>
{{-- End of head content --}}
{{-- Body Content --}}
<div class="container-fixed">
  <div class="grid">
    <div class="card card-grid min-w-full">
      <div class="card-header py-5 flex-wrap justify-between">
        <div class="flex gap-2">
          <select id="category-type-filter" class="select select-sm">
            <option value="all" {{ $type === 'all' ? 'selected' : '' }}>All Categories</option>
            @canAccess('category1')
            <option value="general" {{ $type === 'general' ? 'selected' : '' }}>Category 1 (Umum)</option>
            @endcanAccess
            @canAccess('category2')
            <option value="subject" {{ $type === 'subject' ? 'selected' : '' }}>Category 2 (Mata Pelajaran)</option>
            @endcanAccess
          </select>
        </div>
      </div>
      <div class="card-body">
        <h3 class="text-lg font-semibold text-gray-800 mb-6">
          {{ @$title }} {{ @$page }}
        </h3>
        <div class="scrollable-x-auto">
          <table class="table table-auto table-border">
            <thead>
              <tr>
                <th class="w-28 text-center">No</th>
                <th class="min-w-40">Name</th>
                <th class="min-w-40">Description</th>
                <th class="min-w-40">Type</th>
                @if(
                    ($type === 'general' && ($userPermissions['canEditCategory1'] || $userPermissions['canDeleteCategory1'])) ||
                    ($type === 'subject' && ($userPermissions['canEditCategory2'] || $userPermissions['canDeleteCategory2'])) ||
                    ($type === 'all' && ($userPermissions['canEditCategory1'] || $userPermissions['canEditCategory2'] || $userPermissions['canDeleteCategory1'] || $userPermissions['canDeleteCategory2']))
                )
                <th class="w-auto text-center">Action</th>
                @endif
              </tr>
            </thead>
            <tbody>
              @if(count($categories) === 0)
                @php
                $hasActionColumn = (
                    ($type === 'general' && ($userPermissions['canEditCategory1'] || $userPermissions['canDeleteCategory1'])) ||
                    ($type === 'subject' && ($userPermissions['canEditCategory2'] || $userPermissions['canDeleteCategory2'])) ||
                    ($type === 'all' && ($userPermissions['canEditCategory1'] || $userPermissions['canEditCategory2'] || $userPermissions['canDeleteCategory1'] || $userPermissions['canDeleteCategory2']))
                );
                @endphp
                <tr>
                  <td colspan="{{ $hasActionColumn ? 5 : 4 }}" class="text-center py-4">No categories found</td>
                </tr>
              @else
                @foreach($categories as $index => $category)
                  <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>{{ $category['name'] }}</td>
                    <td>{{ $category['description'] ?? '-' }}</td>
                    <td>{{ $category['type_label'] }}</td>
                    @if(
                        ($category['type'] === 'general' && ($userPermissions['canEditCategory1'] || $userPermissions['canDeleteCategory1'])) ||
                        ($category['type'] === 'subject' && ($userPermissions['canEditCategory2'] || $userPermissions['canDeleteCategory2']))
                    )
                    <td class="text-center">
                      <div class="flex justify-center gap-2">
                        @if(
                            ($category['type'] === 'general' && $userPermissions['canEditCategory1']) ||
                            ($category['type'] === 'subject' && $userPermissions['canEditCategory2'])
                        )
                        <a href="{{ url('reference/categories/' . $category['id'] . '/edit') }}" class="btn btn-icon btn-outline btn-warning btn-sm btn-edit">
                          <i class="fa-solid fa-pen-to-square"></i>
                        </a>
                        @endif
                        @if(
                            ($category['type'] === 'general' && $userPermissions['canDeleteCategory1']) ||
                            ($category['type'] === 'subject' && $userPermissions['canDeleteCategory2'])
                        )
                        <button class="btn btn-icon btn-outline btn-danger btn-sm btn-delete" data-id="{{ $category['id'] }}">
                          <i class="fa-solid fa-trash"></i>
                        </button>
                        @endif
                      </div>
                    </td>
                    @endif
                  </tr>
                @endforeach
              @endif
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
{{-- End of body content --}}
@endsection

@push('scripts')
  <script>
    // Debug permissions
    console.log('User permissions (category_view):', {
      canEditCategory1: {{ $userPermissions['canEditCategory1'] ? 'true' : 'false' }},
      canEditCategory2: {{ $userPermissions['canEditCategory2'] ? 'true' : 'false' }},
      canDeleteCategory1: {{ $userPermissions['canDeleteCategory1'] ? 'true' : 'false' }},
      canDeleteCategory2: {{ $userPermissions['canDeleteCategory2'] ? 'true' : 'false' }}
    });

    // Category type filter change event
    $('#category-type-filter').on('change', function() {
      const type = $(this).val();
      // Update URL with query parameter instead of redirecting to a different route
      const url = new URL(window.location.href);
      url.searchParams.set('type', type);
      window.location.href = url.toString();
    });

    // Attach event handlers for delete buttons
    $('.btn-delete').on('click', function() {
      const id = $(this).data('id');
      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, delete it!"
      }).then((result) => {
        if (result.isConfirmed) {
          $.ajax({
            url: "{{ url('reference/categories') }}/" + id,
            method: 'DELETE',
            data: {
              '_token': '{{ csrf_token() }}'
            },
            success: function(res){
              myToast(res.status, res.message);
              // Reload page after successful deletion
              window.location.reload();
            },
            error: function(err){
              let res = err.responseJSON;
              myToast(res.status, res.message);
            }
          });
        }
      });
    });
  </script>
@endpush
