<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CollectionController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\KeywordController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\ContentController;
use App\Http\Controllers\ExploreController;
use App\Http\Controllers\LogActivityController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Test route for debugging permissions
Route::get('/test-permissions', function () {
    $user = auth()->user();
    if (!$user) {
        return "Not logged in";
    }

    $role_id = $user->role_id;
    $role = $user->role;

    $hasCategory1 = $user->hasPermissions('category1', $role_id);
    $hasCategory2 = $user->hasPermissions('category2', $role_id);

    return [
        'user_id' => $user->id,
        'username' => $user->username,
        'role_id' => $role_id,
        'role_name' => $role->name,
        'has_category1' => $hasCategory1,
        'has_category2' => $hasCategory2
    ];
});

// Test route for viewing permissions in a blade template
Route::get('/test-permissions-view', function () {
    return view('test-permissions');
});

// Test routes with middleware
Route::get('/test-category1', function () {
    return "You have access to category1!";
})->middleware('permission:category1');

Route::get('/test-category2', function () {
    return "You have access to category2!";
})->middleware('permission:category2');

Route::get('/login', function () {
    return view('pages.auth.login');
});

Route::post('/login', [AuthController::class, 'login'])->name('login');

Route::middleware('auth')->group(function(){
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/notifications', [NotificationController::class, 'index'])->name('get-notif');
    Route::put('/notifications', [NotificationController::class, 'read'])->name('read-notif');
    Route::get('/notifications/read-all', [NotificationController::class, 'read_all'])->name('read-all-notif');

    // Route::controller(DashboardController::class)->group(function(){
    //     Route::get('/dashboard', 'index')->name('dashboard')->middleware('permission:dashboard');;
    // });

    Route::prefix('explore')->group(function () {
        Route::controller(ExploreController::class)->group(function(){
            Route::get('/', 'index')->name('explore'); // Available to all users
            Route::get('/download/{id}', 'download')->middleware('permission:download.explores.photo|download.explores.illustration|download.explores.video|download.explores.audio|download.explores.document');
            Route::put('/favorite', 'favorite');
            Route::delete('/delete/{id}', 'destroy')->middleware('permission:delete.explores.photo|delete.explores.illustration|delete.explores.video|delete.explores.audio|delete.explores.document');
            Route::delete('/bulk-delete', 'bulkDestroy')->middleware('permission:bulk delete.explores.photo|bulk delete.explores.illustration|bulk delete.explores.video|bulk delete.explores.audio|bulk delete.explores.document');

            // api
            Route::get('/downloads-count/{id}', 'downloads_count');
            Route::get('/convert-document/{id}', 'convertDocumentForPreview')->middleware('permission:explores.document');
        });
    });

    Route::prefix('submit-content')->group(function () {
        Route::controller(ContentController::class)->group(function(){
            // Use all view permissions for the main page
            Route::get('/', 'index')->name('content')->middleware('permission:uploads.upload_now|uploads.not_submitted|uploads.pending|uploads.rejected|uploads.approved|uploads.published');
            Route::post('/upload', 'upload')->name('upload-content')->middleware('permission:add.uploads');
            Route::put('/update', 'update')->name('update-content');
            Route::delete('/{id}/delete', 'destroy')->middleware('permission:delete.uploads');
            Route::delete('/bulk-delete', 'bulkDestroy')->middleware('permission:bulk delete.uploads');

            // api
            Route::get('/content', 'get_contents')->name('get-content-author');
            Route::get('/search-keywords', 'search_keywords');
            Route::get('/get-all-keywords', 'get_all_keywords');
            Route::get('/get-keywords/{contentId}', 'get_keywords');
            Route::get('/get-reviews/{contentId}', 'get_reviews');
        });
    });

    Route::prefix('review-content')->group(function () {
        Route::controller(ReviewController::class)->group(function(){
            Route::get('/', 'index')->name('review')->middleware('permission:reviews');
            Route::put('/approval', 'approval');
            Route::put('/publication', 'publication');
            // Route::delete('/{id}/delete', 'destroy');
            // Route::delete('/bulk-delete', 'bulkDestroy');

            Route::get('/content', 'get_contents')->name('get-content');
            Route::get('/get-keywords/{contentId}', 'get_keywords');
            Route::get('/get-reviews/{contentId}', 'get_reviews');
            Route::get('/view-document/{id}', 'viewDocument')->name('view-document');
        });
    });

    Route::prefix('manage')->group(function () {
        Route::controller(UserController::class)->group(function(){
            Route::get('/account', 'index')->name('account')->middleware('permission:users');
            Route::get('/account/create', 'create')->name('account-create')->middleware('permission:add.users');
            Route::post('/account/create', 'store')->name('account-store')->middleware('permission:add.users');
            Route::get('/account/{id}/edit', 'edit')->name('account-edit')->middleware('permission:edit.users');
            Route::put('/account/{id}/update', 'update')->name('account-update')->middleware('permission:edit.users');
            Route::delete('/account/{id}', 'destroy')->name('account-delete')->middleware('permission:delete.users');

            // api
            Route::get('/account-datatable', 'users_dataTable')->name('account-datatable');
        });

        Route::controller(RoleController::class)->group(function(){
            Route::get('/roles', 'index')->name('roles')->middleware('permission:roles');
            Route::get('/roles/create', 'create')->name('roles-create')->middleware('permission:add.roles');
            Route::post('/roles/create', 'store')->name('roles-store')->middleware('permission:add.roles');
            Route::get('/roles/{id}/edit', 'edit')->name('roles-edit')->middleware('permission:edit.roles');
            Route::put('/roles/{id}/update', 'update')->name('roles-update')->middleware('permission:edit.roles');
            Route::delete('/roles/{id}', 'destroy')->name('roles-delete')->middleware('permission:delete.roles');

            // api
            Route::get('/roles-datatable', 'roles_dataTable')->name('roles-datatable');
        });

        Route::controller(LogActivityController::class)->group(function(){
            Route::get('/activity', 'index')->name('activities')->middleware('permission:activities');
            // api
            Route::get('/activities-year', 'activities_year')->name('get-activities-year');
            Route::get('/activities', 'activities')->name('get-activities');
        });
    });

    Route::prefix('reference')->group(function () {
        Route::controller(CategoryController::class)->group(function(){
            // General routes for categories
            Route::get('/categories', 'index')->name('categories')->middleware('permission:category1|category2');

            Route::get('/categories/create', 'create')->name('categories-create')->middleware('permission:add.category1|add.category2');
            Route::post('/categories/create', 'store')->name('categories-store')->middleware('permission:add.category1|add.category2');
            Route::get('/categories/{id}/edit', 'edit')->name('categories-edit')->middleware('permission:edit.category1|edit.category2');
            Route::put('/categories/{id}/update', 'update')->name('categories-update')->middleware('permission:edit.category1|edit.category2');
            Route::delete('/categories/{id}', 'destroy')->name('categories-delete')->middleware('permission:delete.category1|delete.category2');

            // API routes
            Route::get('/categories-datatable', 'categories_dataTable')->name('categories-datatable');
            Route::get('/categories-by-type/{type}', 'getCategoriesByType')->name('categories-by-type');
        });

        Route::controller(KeywordController::class)->group(function(){
            Route::get('/keywords', 'index')->name('keywords')->middleware('permission:keywords');
            Route::get('/keywords/create', 'create')->name('keywords-create')->middleware('permission:add.keywords');
            Route::post('/keywords/create', 'store')->name('keywords-store')->middleware('permission:add.keywords');
            Route::get('/keywords/{id}/edit', 'edit')->name('keywords-edit')->middleware('permission:edit.keywords');
            Route::put('/keywords/{id}/update', 'update')->name('keywords-update')->middleware('permission:edit.keywords');
            Route::delete('/keywords/{id}', 'destroy')->name('keywords-delete')->middleware('permission:delete.keywords');

            // api
            Route::get('/keywords-datatable', 'keywords_dataTable')->name('keywords-datatable');
        });
    });

    Route::prefix('profiles')->group(function () {
        Route::controller(ProfileController::class)->group(function(){
            Route::get('/', 'index')->name('profiles')->middleware('permission:profiles');
            Route::put('/{id}/update', 'update')->middleware('permission:edit.profiles');

            // api
            Route::get('/works', 'works')->name('get-works')->middleware('permission:profiles');
            Route::get('/favorites', 'favorites')->name('get-favorites')->middleware('permission:profiles');
            Route::put('/favorites/remove/{id}', 'remove_favorite')->middleware('permission:profiles');
            Route::get('/content/{id}', 'show_content')->middleware('permission:profiles');
            Route::get('/convert-document/{id}', 'convertDocumentForPreview')->middleware('permission:profiles');
        });
    });

    Route::controller(CollectionController::class)->group(function(){
        Route::get('/collections', 'index')->name("get-collections");
        Route::get('/collections/paginate', 'get_paginate')->name("get-collections-paginate");
        Route::get('/collections/{id}', 'show');
        Route::get('/collections/content/{id}', 'show_content');
        Route::post('/collections/create', 'store')->name("store-collection");
        Route::post('/collections/add', 'add_content')->name("add-item-collection");
        Route::post('/collections/remove', 'remove_content')->name("remove-item-collection");
        Route::put('/collections/{id}/update', 'update');
        Route::delete('/collections/{id}/delete', 'destroy');
    });
});