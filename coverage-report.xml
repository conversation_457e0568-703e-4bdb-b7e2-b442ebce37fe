<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1742271994">
  <project timestamp="1742271994">
    <package name="App\Http\Controllers">
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/AuthController.php">
        <class name="App\Http\Controllers\AuthController" namespace="App\Http\Controllers">
          <metrics complexity="5" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="17" coveredstatements="0" elements="20" coveredelements="0"/>
        </class>
        <line num="14" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="15" type="stmt" count="0"/>
        <line num="18" type="method" name="login" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="41" type="method" name="logout" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <metrics loc="46" ncloc="46" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="17" coveredstatements="0" elements="20" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/CategoryController.php">
        <class name="App\Http\Controllers\CategoryController" namespace="App\Http\Controllers">
          <metrics complexity="16" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="51" coveredstatements="0" elements="60" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="22" type="method" name="index" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="32" type="method" name="categories_datatable" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="41" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="54" type="method" name="store" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="74" type="method" name="show" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="82" type="method" name="edit" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="96" type="method" name="update" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="116" type="method" name="destroy" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="123" type="stmt" count="0"/>
        <line num="124" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <metrics loc="134" ncloc="112" classes="1" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="51" coveredstatements="0" elements="60" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/CollectionController.php">
        <class name="App\Http\Controllers\CollectionController" namespace="App\Http\Controllers">
          <metrics complexity="25" methods="12" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="103" coveredstatements="0" elements="115" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="19" type="method" name="index" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="38" type="method" name="get_paginate" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="60" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="68" type="method" name="store" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="87" type="method" name="add_content" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="97" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="105" type="method" name="remove_content" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <line num="112" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="114" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="126" type="method" name="show" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="131" type="stmt" count="0"/>
        <line num="132" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="135" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="140" type="stmt" count="0"/>
        <line num="144" type="method" name="show_content" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="147" type="stmt" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="151" type="stmt" count="0"/>
        <line num="152" type="stmt" count="0"/>
        <line num="153" type="stmt" count="0"/>
        <line num="154" type="stmt" count="0"/>
        <line num="155" type="stmt" count="0"/>
        <line num="156" type="stmt" count="0"/>
        <line num="157" type="stmt" count="0"/>
        <line num="158" type="stmt" count="0"/>
        <line num="165" type="method" name="edit" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="168" type="stmt" count="0"/>
        <line num="173" type="method" name="update" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="178" type="stmt" count="0"/>
        <line num="179" type="stmt" count="0"/>
        <line num="180" type="stmt" count="0"/>
        <line num="181" type="stmt" count="0"/>
        <line num="182" type="stmt" count="0"/>
        <line num="183" type="stmt" count="0"/>
        <line num="184" type="stmt" count="0"/>
        <line num="185" type="stmt" count="0"/>
        <line num="186" type="stmt" count="0"/>
        <line num="193" type="method" name="destroy" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="196" type="stmt" count="0"/>
        <line num="198" type="stmt" count="0"/>
        <line num="199" type="stmt" count="0"/>
        <line num="200" type="stmt" count="0"/>
        <line num="201" type="stmt" count="0"/>
        <line num="202" type="stmt" count="0"/>
        <line num="203" type="stmt" count="0"/>
        <line num="204" type="stmt" count="0"/>
        <line num="205" type="stmt" count="0"/>
        <line num="206" type="stmt" count="0"/>
        <metrics loc="210" ncloc="187" classes="1" methods="12" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="103" coveredstatements="0" elements="115" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/ContentController.php">
        <class name="App\Http\Controllers\ContentController" namespace="App\Http\Controllers">
          <metrics complexity="21" methods="10" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="101" coveredstatements="0" elements="111" coveredelements="0"/>
        </class>
        <line num="15" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="22" type="method" name="index" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="31" type="method" name="get_contents" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="52" type="method" name="get_reviews" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="70" type="method" name="search_keywords" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="88" type="method" name="get_keywords" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="106" type="method" name="upload" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <line num="112" type="stmt" count="0"/>
        <line num="114" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="123" type="stmt" count="0"/>
        <line num="124" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="129" type="method" name="update" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="131" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="140" type="stmt" count="0"/>
        <line num="143" type="stmt" count="0"/>
        <line num="144" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <line num="146" type="stmt" count="0"/>
        <line num="149" type="method" name="destroy" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="152" type="stmt" count="0"/>
        <line num="154" type="stmt" count="0"/>
        <line num="155" type="stmt" count="0"/>
        <line num="156" type="stmt" count="0"/>
        <line num="157" type="stmt" count="0"/>
        <line num="158" type="stmt" count="0"/>
        <line num="159" type="stmt" count="0"/>
        <line num="160" type="stmt" count="0"/>
        <line num="161" type="stmt" count="0"/>
        <line num="162" type="stmt" count="0"/>
        <line num="163" type="stmt" count="0"/>
        <line num="167" type="method" name="bulkDestroy" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="170" type="stmt" count="0"/>
        <line num="172" type="stmt" count="0"/>
        <line num="173" type="stmt" count="0"/>
        <line num="174" type="stmt" count="0"/>
        <line num="175" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="177" type="stmt" count="0"/>
        <line num="178" type="stmt" count="0"/>
        <line num="179" type="stmt" count="0"/>
        <line num="180" type="stmt" count="0"/>
        <line num="181" type="stmt" count="0"/>
        <metrics loc="185" ncloc="185" classes="1" methods="10" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="101" coveredstatements="0" elements="111" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/Controller.php">
        <class name="App\Http\Controllers\Controller" namespace="App\Http\Controllers">
          <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
        </class>
        <metrics loc="9" ncloc="8" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/DashboardController.php">
        <class name="App\Http\Controllers\DashboardController" namespace="App\Http\Controllers">
          <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
        </class>
        <line num="9" type="method" name="index" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="11" type="stmt" count="0"/>
        <metrics loc="14" ncloc="14" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/ExploreController.php">
        <class name="App\Http\Controllers\ExploreController" namespace="App\Http\Controllers">
          <metrics complexity="13" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="47" coveredstatements="0" elements="52" coveredelements="0"/>
        </class>
        <line num="15" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="20" type="method" name="index" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="45" type="method" name="download" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="60" type="method" name="downloads_count" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="78" type="method" name="favorite" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <metrics loc="96" ncloc="96" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="47" coveredstatements="0" elements="52" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/KeywordController.php">
        <class name="App\Http\Controllers\KeywordController" namespace="App\Http\Controllers">
          <metrics complexity="14" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="51" coveredstatements="0" elements="60" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="21" type="method" name="index" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="31" type="method" name="keywords_datatable" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="40" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="53" type="method" name="store" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="74" type="method" name="show" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="82" type="method" name="edit" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="96" type="method" name="update" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="116" type="method" name="destroy" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="123" type="stmt" count="0"/>
        <line num="124" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <metrics loc="134" ncloc="112" classes="1" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="51" coveredstatements="0" elements="60" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/LogActivityController.php">
        <class name="App\Http\Controllers\LogActivityController" namespace="App\Http\Controllers">
          <metrics complexity="6" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="26" coveredstatements="0" elements="30" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="13" type="stmt" count="0"/>
        <line num="16" type="method" name="index" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="21" type="method" name="activities_year" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="40" type="method" name="activities" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <metrics loc="59" ncloc="59" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="26" coveredstatements="0" elements="30" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/NotificationController.php">
        <class name="App\Http\Controllers\NotificationController" namespace="App\Http\Controllers">
          <metrics complexity="7" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="34" coveredstatements="0" elements="38" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="17" type="method" name="index" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="35" type="method" name="read" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="53" type="method" name="read_all" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <metrics loc="71" ncloc="71" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="34" coveredstatements="0" elements="38" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/ProfileController.php">
        <class name="App\Http\Controllers\ProfileController" namespace="App\Http\Controllers">
          <metrics complexity="12" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="58" coveredstatements="0" elements="65" coveredelements="0"/>
        </class>
        <line num="13" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="17" type="method" name="index" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="23" type="method" name="update" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="43" type="method" name="works" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="61" type="method" name="favorites" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="79" type="method" name="show_content" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="97" type="method" name="remove_favorite" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <metrics loc="115" ncloc="115" classes="1" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="58" coveredstatements="0" elements="65" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/ReviewController.php">
        <class name="App\Http\Controllers\ReviewController" namespace="App\Http\Controllers">
          <metrics complexity="11" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="54" coveredstatements="0" elements="60" coveredelements="0"/>
        </class>
        <line num="14" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="19" type="method" name="index" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="32" type="method" name="get_contents" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="53" type="method" name="approval" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="71" type="method" name="publication" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="89" type="method" name="get_reviews" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="97" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <metrics loc="107" ncloc="107" classes="1" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="54" coveredstatements="0" elements="60" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/RoleController.php">
        <class name="App\Http\Controllers\RoleController" namespace="App\Http\Controllers">
          <metrics complexity="12" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="57" coveredstatements="0" elements="66" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="13" type="stmt" count="0"/>
        <line num="21" type="method" name="index" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="31" type="method" name="roles_dataTable" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="40" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="54" type="method" name="store" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="74" type="method" name="show" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="82" type="method" name="edit" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="97" type="method" name="update" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <line num="117" type="method" name="destroy" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="123" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="132" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="135" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <metrics loc="141" ncloc="119" classes="1" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="57" coveredstatements="0" elements="66" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Controllers/UserController.php">
        <class name="App\Http\Controllers\UserController" namespace="App\Http\Controllers">
          <metrics complexity="14" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="61" coveredstatements="0" elements="70" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="13" type="stmt" count="0"/>
        <line num="20" type="method" name="index" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="30" type="method" name="users_dataTable" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="40" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="54" type="method" name="store" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="78" type="method" name="show" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="86" type="method" name="edit" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="101" type="method" name="update" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <line num="112" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="124" type="method" name="destroy" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <line num="132" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="135" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="140" type="stmt" count="0"/>
        <line num="142" type="stmt" count="0"/>
        <line num="143" type="stmt" count="0"/>
        <line num="144" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <metrics loc="148" ncloc="126" classes="1" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="61" coveredstatements="0" elements="70" coveredelements="0"/>
      </file>
    </package>
    <package name="App\Http\Middleware">
      <file name="/home/<USER>/grafindo-media-library/app/Http/Middleware/CheckPermission.php">
        <class name="App\Http\Middleware\CheckPermission" namespace="App\Http\Middleware">
          <metrics complexity="5" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="14" coveredstatements="0" elements="15" coveredelements="0"/>
        </class>
        <line num="16" type="method" name="handle" visibility="public" complexity="5" crap="30" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <metrics loc="43" ncloc="35" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="14" coveredstatements="0" elements="15" coveredelements="0"/>
      </file>
    </package>
    <package name="App\Http\Requests">
      <file name="/home/<USER>/grafindo-media-library/app/Http/Requests/ContentRequest.php">
        <class name="App\Http\Requests\ContentRequest" namespace="App\Http\Requests">
          <metrics complexity="8" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="24" coveredstatements="0" elements="29" coveredelements="0"/>
        </class>
        <line num="13" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="15" type="stmt" count="0"/>
        <line num="20" type="method" name="authorize" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="30" type="method" name="rules" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="45" type="method" name="isReleaseDocExist" visibility="private" complexity="3" crap="12" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="59" type="method" name="failedValidation" visibility="protected" complexity="1" crap="2" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <metrics loc="68" ncloc="60" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="24" coveredstatements="0" elements="29" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Http/Requests/UserRequest.php">
        <class name="App\Http\Requests\UserRequest" namespace="App\Http\Requests">
          <metrics complexity="5" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="23" coveredstatements="0" elements="27" coveredelements="0"/>
        </class>
        <line num="14" type="method" name="authorize" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="24" type="method" name="rules" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="36" type="method" name="messages" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="49" type="method" name="failedValidation" visibility="protected" complexity="1" crap="2" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <metrics loc="62" ncloc="49" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="23" coveredstatements="0" elements="27" coveredelements="0"/>
      </file>
    </package>
    <package name="App\Models">
      <file name="/home/<USER>/grafindo-media-library/app/Models/Category.php">
        <class name="App\Models\Category" namespace="App\Models">
          <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
        </class>
        <metrics loc="17" ncloc="17" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/Collection.php">
        <class name="App\Models\Collection" namespace="App\Models">
          <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
        </class>
        <line num="17" type="method" name="contents" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <metrics loc="22" ncloc="22" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/CollectionContent.php">
        <class name="App\Models\CollectionContent" namespace="App\Models">
          <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
        </class>
        <line num="18" type="method" name="content" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="23" type="method" name="collection" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <metrics loc="28" ncloc="28" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/Content.php">
        <class name="App\Models\Content" namespace="App\Models">
          <metrics complexity="6" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="0" elements="12" coveredelements="0"/>
        </class>
        <line num="32" type="method" name="author" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="37" type="method" name="category" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="42" type="method" name="keywords" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="47" type="method" name="favorite" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="52" type="method" name="favorites" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="57" type="method" name="collections" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <metrics loc="62" ncloc="62" classes="1" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="0" elements="12" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/ContentKeyword.php">
        <class name="App\Models\ContentKeyword" namespace="App\Models">
          <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
        </class>
        <line num="18" type="method" name="content" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="23" type="method" name="keyword" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <metrics loc="28" ncloc="28" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/ContentReview.php">
        <class name="App\Models\ContentReview" namespace="App\Models">
          <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
        </class>
        <line num="22" type="method" name="reviewer" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <metrics loc="28" ncloc="28" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/Download.php">
        <class name="App\Models\Download" namespace="App\Models">
          <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
        </class>
        <metrics loc="19" ncloc="19" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/Favorite.php">
        <class name="App\Models\Favorite" namespace="App\Models">
          <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
        </class>
        <line num="18" type="method" name="user" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="23" type="method" name="content" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <metrics loc="28" ncloc="28" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/Keyword.php">
        <class name="App\Models\Keyword" namespace="App\Models">
          <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
        </class>
        <line num="16" type="method" name="contents" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <metrics loc="21" ncloc="21" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/LogActivity.php">
        <class name="App\Models\LogActivity" namespace="App\Models">
          <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
        </class>
        <line num="21" type="method" name="content" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="26" type="method" name="user" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <metrics loc="31" ncloc="31" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/Notification.php">
        <class name="App\Models\Notification" namespace="App\Models">
          <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
        </class>
        <line num="21" type="method" name="user" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <metrics loc="26" ncloc="26" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/Permission.php">
        <class name="App\Models\Permission" namespace="App\Models">
          <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
        </class>
        <metrics loc="14" ncloc="14" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/Role.php">
        <class name="App\Models\Role" namespace="App\Models">
          <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
        </class>
        <line num="17" type="method" name="permissions" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <metrics loc="22" ncloc="22" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/RolePermission.php">
        <class name="App\Models\RolePermission" namespace="App\Models">
          <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
        </class>
        <line num="17" type="method" name="permissions" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <metrics loc="22" ncloc="22" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Models/User.php">
        <class name="App\Models\User" namespace="App\Models">
          <metrics complexity="3" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="9" coveredstatements="0" elements="12" coveredelements="0"/>
        </class>
        <line num="48" type="method" name="casts" visibility="protected" complexity="1" crap="2" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="56" type="method" name="role" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="61" type="method" name="hasPermissions" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <metrics loc="69" ncloc="52" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="9" coveredstatements="0" elements="12" coveredelements="0"/>
      </file>
    </package>
    <package name="App\Providers">
      <file name="/home/<USER>/grafindo-media-library/app/Providers/AppServiceProvider.php">
        <class name="App\Providers\AppServiceProvider" namespace="App\Providers">
          <metrics complexity="5" methods="2" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="23" coveredstatements="17" elements="25" coveredelements="18"/>
        </class>
        <line num="46" type="method" name="register" visibility="public" complexity="1" crap="1" count="1"/>
        <line num="48" type="stmt" count="1"/>
        <line num="49" type="stmt" count="1"/>
        <line num="50" type="stmt" count="1"/>
        <line num="51" type="stmt" count="1"/>
        <line num="52" type="stmt" count="1"/>
        <line num="53" type="stmt" count="1"/>
        <line num="54" type="stmt" count="1"/>
        <line num="55" type="stmt" count="1"/>
        <line num="56" type="stmt" count="1"/>
        <line num="57" type="stmt" count="1"/>
        <line num="58" type="stmt" count="1"/>
        <line num="59" type="stmt" count="1"/>
        <line num="60" type="stmt" count="1"/>
        <line num="61" type="stmt" count="1"/>
        <line num="62" type="stmt" count="1"/>
        <line num="68" type="method" name="boot" visibility="public" complexity="4" crap="10.75" count="1"/>
        <line num="70" type="stmt" count="1"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="81" type="stmt" count="1"/>
        <metrics loc="84" ncloc="78" classes="1" methods="2" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="23" coveredstatements="17" elements="25" coveredelements="18"/>
      </file>
    </package>
    <package name="App\Repositories">
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/AuthRepository.php">
        <class name="App\Repositories\AuthRepository" namespace="App\Repositories">
          <metrics complexity="2" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
        </class>
        <line num="9" type="method" name="findUserByEmail" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="10" type="stmt" count="0"/>
        <line num="13" type="method" name="createUser" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <metrics loc="16" ncloc="16" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="0" elements="4" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/CategoryRepository.php">
        <class name="App\Repositories\CategoryRepository" namespace="App\Repositories">
          <metrics complexity="9" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="14" coveredstatements="0" elements="20" coveredelements="0"/>
        </class>
        <line num="10" type="method" name="getCategory" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="12" type="stmt" count="0"/>
        <line num="13" type="stmt" count="0"/>
        <line num="15" type="stmt" count="0"/>
        <line num="19" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="24" type="method" name="findExisting" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="29" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="34" type="method" name="update" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="45" type="method" name="delete" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <metrics loc="55" ncloc="55" classes="1" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="14" coveredstatements="0" elements="20" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/CollectionContentRepository.php">
        <class name="App\Repositories\CollectionContentRepository" namespace="App\Repositories">
          <metrics complexity="5" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="0" elements="10" coveredelements="0"/>
        </class>
        <line num="10" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="12" type="stmt" count="0"/>
        <line num="14" type="method" name="add" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="19" type="method" name="remove" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="28" type="method" name="removeAll" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <metrics loc="32" ncloc="32" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="0" elements="10" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/CollectionRepository.php">
        <class name="App\Repositories\CollectionRepository" namespace="App\Repositories">
          <metrics complexity="9" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="17" coveredstatements="0" elements="24" coveredelements="0"/>
        </class>
        <line num="10" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="12" type="stmt" count="0"/>
        <line num="15" type="method" name="getAllUserCollections" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="20" type="method" name="getCollectionDetail" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="33" type="method" name="getUserCollections" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="38" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="43" type="method" name="update" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="52" type="method" name="delete" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <metrics loc="60" ncloc="60" classes="1" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="17" coveredstatements="0" elements="24" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/ContentRepository.php">
        <class name="App\Repositories\ContentRepository" namespace="App\Repositories">
          <metrics complexity="14" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="73" coveredstatements="0" elements="82" coveredelements="0"/>
        </class>
        <line num="10" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="12" type="stmt" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="15" type="stmt" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="24" type="method" name="getContent" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="49" type="method" name="getPublishedContent" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="85" type="method" name="countContent" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="118" type="method" name="countContentByAuthor" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="153" type="stmt" count="0"/>
        <line num="155" type="stmt" count="0"/>
        <line num="158" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="160" type="stmt" count="0"/>
        <line num="163" type="method" name="update" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="165" type="stmt" count="0"/>
        <line num="167" type="stmt" count="0"/>
        <line num="169" type="stmt" count="0"/>
        <line num="172" type="method" name="delete" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="174" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="178" type="stmt" count="0"/>
        <line num="181" type="method" name="bulkDelete" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="184" type="stmt" count="0"/>
        <line num="185" type="stmt" count="0"/>
        <line num="186" type="stmt" count="0"/>
        <metrics loc="190" ncloc="190" classes="1" methods="9" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="73" coveredstatements="0" elements="82" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/DownloadRepository.php">
        <class name="App\Repositories\DownloadRepository" namespace="App\Repositories">
          <metrics complexity="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
        </class>
        <line num="10" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="12" type="stmt" count="0"/>
        <metrics loc="15" ncloc="15" classes="1" methods="1" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="0" elements="2" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/FavoriteRepository.php">
        <class name="App\Repositories\FavoriteRepository" namespace="App\Repositories">
          <metrics complexity="5" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="0" elements="10" coveredelements="0"/>
        </class>
        <line num="10" type="method" name="find" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="12" type="stmt" count="0"/>
        <line num="15" type="method" name="getUserFavorites" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="20" type="method" name="add" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="25" type="method" name="remove" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <metrics loc="33" ncloc="33" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="0" elements="10" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/KeywordRepository.php">
        <class name="App\Repositories\KeywordRepository" namespace="App\Repositories">
          <metrics complexity="10" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="15" coveredstatements="0" elements="22" coveredelements="0"/>
        </class>
        <line num="10" type="method" name="getKeyword" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="12" type="stmt" count="0"/>
        <line num="13" type="stmt" count="0"/>
        <line num="15" type="stmt" count="0"/>
        <line num="19" type="method" name="search" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="24" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="29" type="method" name="findExisting" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="34" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="39" type="method" name="update" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="50" type="method" name="delete" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <metrics loc="60" ncloc="60" classes="1" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="15" coveredstatements="0" elements="22" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/LogRepository.php">
        <class name="App\Repositories\LogRepository" namespace="App\Repositories">
          <metrics complexity="3" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="20" coveredstatements="0" elements="23" coveredelements="0"/>
        </class>
        <line num="11" type="method" name="log" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="13" type="stmt" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="15" type="stmt" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="22" type="method" name="getYears" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="32" type="method" name="getActivities" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <metrics loc="43" ncloc="43" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="20" coveredstatements="0" elements="23" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/NotificationRepository.php">
        <class name="App\Repositories\NotificationRepository" namespace="App\Repositories">
          <metrics complexity="6" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="22" coveredstatements="0" elements="26" coveredelements="0"/>
        </class>
        <line num="11" type="method" name="getNotifications" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="13" type="stmt" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="15" type="stmt" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="22" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="33" type="method" name="readNotification" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="44" type="method" name="readAllNotification" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <metrics loc="54" ncloc="54" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="22" coveredstatements="0" elements="26" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/PermissionRepository.php">
        <class name="App\Repositories\PermissionRepository" namespace="App\Repositories">
          <metrics complexity="3" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="4" coveredstatements="0" elements="6" coveredelements="0"/>
        </class>
        <line num="9" type="method" name="getPermission" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="11" type="stmt" count="0"/>
        <line num="12" type="stmt" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="18" type="method" name="getPermissionGroup" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <metrics loc="22" ncloc="22" classes="1" methods="2" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="4" coveredstatements="0" elements="6" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/ReviewRepository.php">
        <class name="App\Repositories\ReviewRepository" namespace="App\Repositories">
          <metrics complexity="7" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="13" coveredstatements="0" elements="18" coveredelements="0"/>
        </class>
        <line num="11" type="method" name="findOneContent" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="13" type="stmt" count="0"/>
        <line num="16" type="method" name="getReviews" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="25" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="30" type="method" name="approval" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="39" type="method" name="publication" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <metrics loc="47" ncloc="47" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="13" coveredstatements="0" elements="18" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/RolePermissionRepository.php">
        <class name="App\Repositories\RolePermissionRepository" namespace="App\Repositories">
          <metrics complexity="3" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="0" elements="9" coveredelements="0"/>
        </class>
        <line num="9" type="method" name="getRolePermissions" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="11" type="stmt" count="0"/>
        <line num="14" type="method" name="updateOrCreate" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="16" type="stmt" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="22" type="method" name="deleteRolePermissions" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <metrics loc="26" ncloc="26" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="0" elements="9" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/RoleRepository.php">
        <class name="App\Repositories\RoleRepository" namespace="App\Repositories">
          <metrics complexity="8" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="13" coveredstatements="0" elements="18" coveredelements="0"/>
        </class>
        <line num="10" type="method" name="getRoles" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="12" type="stmt" count="0"/>
        <line num="13" type="stmt" count="0"/>
        <line num="15" type="stmt" count="0"/>
        <line num="19" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="24" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="29" type="method" name="update" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="40" type="method" name="delete" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <metrics loc="50" ncloc="50" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="13" coveredstatements="0" elements="18" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Repositories/UserRepository.php">
        <class name="App\Repositories\UserRepository" namespace="App\Repositories">
          <metrics complexity="7" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="16" coveredelements="0"/>
        </class>
        <line num="10" type="method" name="getAll" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="12" type="stmt" count="0"/>
        <line num="15" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="17" type="stmt" count="0"/>
        <line num="20" type="method" name="create" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="25" type="method" name="update" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="36" type="method" name="delete" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <metrics loc="46" ncloc="46" classes="1" methods="5" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="16" coveredelements="0"/>
      </file>
    </package>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/AuthRepositoryInterface.php">
      <metrics loc="8" ncloc="8" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/CategoryRepositoryInterface.php">
      <metrics loc="12" ncloc="12" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/CollectionContentRepositoryInterface.php">
      <metrics loc="11" ncloc="11" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/CollectionRepositoryInterface.php">
      <metrics loc="14" ncloc="14" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/ContentRepositoryInterface.php">
      <metrics loc="16" ncloc="16" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/DownloadRepositoryInterface.php">
      <metrics loc="8" ncloc="8" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/FavoriteRepositoryInterface.php">
      <metrics loc="11" ncloc="11" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/KeywordRepositoryInterface.php">
      <metrics loc="13" ncloc="13" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/LogRepositoryInterface.php">
      <metrics loc="10" ncloc="10" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/NotificationRepositoryInterface.php">
      <metrics loc="11" ncloc="11" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/PermissionRepositoryInterface.php">
      <metrics loc="8" ncloc="8" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/ReviewRepositoryInterface.php">
      <metrics loc="12" ncloc="12" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/RolePermissionRepositoryInterface.php">
      <metrics loc="9" ncloc="9" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/RoleRepositoryInterface.php">
      <metrics loc="11" ncloc="11" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/grafindo-media-library/app/Repositories/Contracts/UserRepositoryInterface.php">
      <metrics loc="11" ncloc="11" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <package name="App\Services">
      <file name="/home/<USER>/grafindo-media-library/app/Services/AuthService.php">
        <class name="App\Services\AuthService" namespace="App\Services">
          <metrics complexity="4" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="8" coveredstatements="0" elements="11" coveredelements="0"/>
        </class>
        <line num="13" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="17" type="method" name="login" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="18" type="stmt" count="0"/>
        <line num="19" type="stmt" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="26" type="method" name="logout" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <metrics loc="33" ncloc="33" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="8" coveredstatements="0" elements="11" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Services/CategoryService.php">
        <class name="App\Services\CategoryService" namespace="App\Services">
          <metrics complexity="15" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="80" coveredstatements="0" elements="86" coveredelements="0"/>
        </class>
        <line num="16" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="25" type="method" name="getAll" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="50" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="55" type="method" name="create" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="89" type="method" name="update" visibility="public" complexity="5" crap="30" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="125" type="method" name="delete" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <line num="132" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="135" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="143" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <line num="147" type="stmt" count="0"/>
        <line num="148" type="stmt" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="151" type="stmt" count="0"/>
        <metrics loc="154" ncloc="150" classes="1" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="80" coveredstatements="0" elements="86" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Services/CollectionService.php">
        <class name="App\Services\CollectionService" namespace="App\Services">
          <metrics complexity="25" methods="10" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="125" coveredstatements="0" elements="135" coveredelements="0"/>
        </class>
        <line num="17" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="30" type="method" name="getAllCollections" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="41" type="method" name="getCollections" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="52" type="method" name="getCollectionDetail" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="62" type="method" name="getContentDetail" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="74" type="method" name="create" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="97" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="105" type="method" name="update" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="112" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="114" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="123" type="stmt" count="0"/>
        <line num="124" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="132" type="method" name="delete" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="140" type="stmt" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="142" type="stmt" count="0"/>
        <line num="143" type="stmt" count="0"/>
        <line num="144" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <line num="146" type="stmt" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="151" type="stmt" count="0"/>
        <line num="152" type="stmt" count="0"/>
        <line num="153" type="stmt" count="0"/>
        <line num="154" type="stmt" count="0"/>
        <line num="155" type="stmt" count="0"/>
        <line num="159" type="method" name="add" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="161" type="stmt" count="0"/>
        <line num="163" type="stmt" count="0"/>
        <line num="164" type="stmt" count="0"/>
        <line num="165" type="stmt" count="0"/>
        <line num="166" type="stmt" count="0"/>
        <line num="168" type="stmt" count="0"/>
        <line num="170" type="stmt" count="0"/>
        <line num="171" type="stmt" count="0"/>
        <line num="173" type="stmt" count="0"/>
        <line num="175" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="177" type="stmt" count="0"/>
        <line num="178" type="stmt" count="0"/>
        <line num="179" type="stmt" count="0"/>
        <line num="180" type="stmt" count="0"/>
        <line num="181" type="stmt" count="0"/>
        <line num="182" type="stmt" count="0"/>
        <line num="185" type="stmt" count="0"/>
        <line num="186" type="stmt" count="0"/>
        <line num="187" type="stmt" count="0"/>
        <line num="188" type="stmt" count="0"/>
        <line num="189" type="stmt" count="0"/>
        <line num="190" type="stmt" count="0"/>
        <line num="191" type="stmt" count="0"/>
        <line num="195" type="method" name="remove" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="197" type="stmt" count="0"/>
        <line num="199" type="stmt" count="0"/>
        <line num="200" type="stmt" count="0"/>
        <line num="202" type="stmt" count="0"/>
        <line num="203" type="stmt" count="0"/>
        <line num="204" type="stmt" count="0"/>
        <line num="205" type="stmt" count="0"/>
        <line num="206" type="stmt" count="0"/>
        <line num="207" type="stmt" count="0"/>
        <line num="208" type="stmt" count="0"/>
        <line num="209" type="stmt" count="0"/>
        <line num="212" type="stmt" count="0"/>
        <line num="213" type="stmt" count="0"/>
        <line num="214" type="stmt" count="0"/>
        <line num="215" type="stmt" count="0"/>
        <line num="216" type="stmt" count="0"/>
        <line num="217" type="stmt" count="0"/>
        <line num="218" type="stmt" count="0"/>
        <metrics loc="221" ncloc="221" classes="1" methods="10" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="125" coveredstatements="0" elements="135" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Services/ContentService.php">
        <class name="App\Services\ContentService" namespace="App\Services">
          <metrics complexity="43" methods="12" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="174" coveredstatements="0" elements="186" coveredelements="0"/>
        </class>
        <line num="24" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="41" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="46" type="method" name="getContentByAuthor" visibility="public" complexity="5" crap="30" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="61" type="method" name="countContentByAuthor" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="66" type="method" name="create" visibility="public" complexity="11" crap="132" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="112" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="114" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="123" type="stmt" count="0"/>
        <line num="124" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="131" type="stmt" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="140" type="stmt" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="142" type="stmt" count="0"/>
        <line num="143" type="stmt" count="0"/>
        <line num="146" type="stmt" count="0"/>
        <line num="148" type="stmt" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="151" type="stmt" count="0"/>
        <line num="152" type="stmt" count="0"/>
        <line num="153" type="stmt" count="0"/>
        <line num="157" type="method" name="update" visibility="public" complexity="12" crap="156" count="0"/>
        <line num="159" type="stmt" count="0"/>
        <line num="161" type="stmt" count="0"/>
        <line num="162" type="stmt" count="0"/>
        <line num="164" type="stmt" count="0"/>
        <line num="165" type="stmt" count="0"/>
        <line num="168" type="stmt" count="0"/>
        <line num="169" type="stmt" count="0"/>
        <line num="170" type="stmt" count="0"/>
        <line num="173" type="stmt" count="0"/>
        <line num="174" type="stmt" count="0"/>
        <line num="175" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="177" type="stmt" count="0"/>
        <line num="178" type="stmt" count="0"/>
        <line num="179" type="stmt" count="0"/>
        <line num="180" type="stmt" count="0"/>
        <line num="181" type="stmt" count="0"/>
        <line num="182" type="stmt" count="0"/>
        <line num="184" type="stmt" count="0"/>
        <line num="185" type="stmt" count="0"/>
        <line num="187" type="stmt" count="0"/>
        <line num="189" type="stmt" count="0"/>
        <line num="191" type="stmt" count="0"/>
        <line num="192" type="stmt" count="0"/>
        <line num="195" type="stmt" count="0"/>
        <line num="196" type="stmt" count="0"/>
        <line num="197" type="stmt" count="0"/>
        <line num="198" type="stmt" count="0"/>
        <line num="199" type="stmt" count="0"/>
        <line num="200" type="stmt" count="0"/>
        <line num="201" type="stmt" count="0"/>
        <line num="204" type="stmt" count="0"/>
        <line num="205" type="stmt" count="0"/>
        <line num="206" type="stmt" count="0"/>
        <line num="207" type="stmt" count="0"/>
        <line num="209" type="stmt" count="0"/>
        <line num="210" type="stmt" count="0"/>
        <line num="211" type="stmt" count="0"/>
        <line num="212" type="stmt" count="0"/>
        <line num="213" type="stmt" count="0"/>
        <line num="214" type="stmt" count="0"/>
        <line num="215" type="stmt" count="0"/>
        <line num="219" type="stmt" count="0"/>
        <line num="221" type="stmt" count="0"/>
        <line num="222" type="stmt" count="0"/>
        <line num="223" type="stmt" count="0"/>
        <line num="224" type="stmt" count="0"/>
        <line num="225" type="stmt" count="0"/>
        <line num="226" type="stmt" count="0"/>
        <line num="230" type="method" name="delete" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="232" type="stmt" count="0"/>
        <line num="234" type="stmt" count="0"/>
        <line num="235" type="stmt" count="0"/>
        <line num="237" type="stmt" count="0"/>
        <line num="238" type="stmt" count="0"/>
        <line num="239" type="stmt" count="0"/>
        <line num="240" type="stmt" count="0"/>
        <line num="241" type="stmt" count="0"/>
        <line num="242" type="stmt" count="0"/>
        <line num="243" type="stmt" count="0"/>
        <line num="244" type="stmt" count="0"/>
        <line num="247" type="stmt" count="0"/>
        <line num="249" type="stmt" count="0"/>
        <line num="251" type="stmt" count="0"/>
        <line num="252" type="stmt" count="0"/>
        <line num="253" type="stmt" count="0"/>
        <line num="254" type="stmt" count="0"/>
        <line num="255" type="stmt" count="0"/>
        <line num="259" type="method" name="bulkDelete" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="261" type="stmt" count="0"/>
        <line num="263" type="stmt" count="0"/>
        <line num="265" type="stmt" count="0"/>
        <line num="266" type="stmt" count="0"/>
        <line num="267" type="stmt" count="0"/>
        <line num="268" type="stmt" count="0"/>
        <line num="269" type="stmt" count="0"/>
        <line num="270" type="stmt" count="0"/>
        <line num="271" type="stmt" count="0"/>
        <line num="272" type="stmt" count="0"/>
        <line num="275" type="stmt" count="0"/>
        <line num="277" type="stmt" count="0"/>
        <line num="278" type="stmt" count="0"/>
        <line num="279" type="stmt" count="0"/>
        <line num="280" type="stmt" count="0"/>
        <line num="281" type="stmt" count="0"/>
        <line num="282" type="stmt" count="0"/>
        <line num="286" type="method" name="getCategory" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="288" type="stmt" count="0"/>
        <line num="291" type="method" name="getKeywords" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="293" type="stmt" count="0"/>
        <line num="295" type="stmt" count="0"/>
        <line num="298" type="method" name="searchKeywords" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="300" type="stmt" count="0"/>
        <line num="303" type="method" name="getReviews" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="306" type="stmt" count="0"/>
        <line num="307" type="stmt" count="0"/>
        <line num="308" type="stmt" count="0"/>
        <line num="310" type="stmt" count="0"/>
        <line num="311" type="stmt" count="0"/>
        <line num="312" type="stmt" count="0"/>
        <line num="313" type="stmt" count="0"/>
        <line num="314" type="stmt" count="0"/>
        <metrics loc="317" ncloc="313" classes="1" methods="12" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="174" coveredstatements="0" elements="186" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Services/ExploreService.php">
        <class name="App\Services\ExploreService" namespace="App\Services">
          <metrics complexity="16" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="71" coveredstatements="0" elements="77" coveredelements="0"/>
        </class>
        <line num="20" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="35" type="method" name="getOneContent" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="46" type="method" name="getCategory" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="57" type="method" name="getPublishedContent" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="63" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="68" type="method" name="download" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="108" type="method" name="favorite" visibility="public" complexity="5" crap="30" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="112" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="114" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="123" type="stmt" count="0"/>
        <line num="124" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="132" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="135" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <metrics loc="142" ncloc="142" classes="1" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="71" coveredstatements="0" elements="77" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Services/KeywordService.php">
        <class name="App\Services\KeywordService" namespace="App\Services">
          <metrics complexity="15" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="80" coveredstatements="0" elements="86" coveredelements="0"/>
        </class>
        <line num="18" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="27" type="method" name="getAll" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="51" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="56" type="method" name="create" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="62" type="stmt" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="65" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="91" type="method" name="update" visibility="public" complexity="5" crap="30" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="97" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <line num="114" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="124" type="method" name="delete" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="131" type="stmt" count="0"/>
        <line num="132" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="135" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="143" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <line num="146" type="stmt" count="0"/>
        <line num="147" type="stmt" count="0"/>
        <line num="148" type="stmt" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <metrics loc="152" ncloc="148" classes="1" methods="6" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="80" coveredstatements="0" elements="86" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Services/LogActivityService.php">
        <class name="App\Services\LogActivityService" namespace="App\Services">
          <metrics complexity="5" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="14" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="17" type="method" name="getActivityYears" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="28" type="method" name="getActivities" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <metrics loc="38" ncloc="38" classes="1" methods="3" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="0" elements="14" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Services/NotificationService.php">
        <class name="App\Services\NotificationService" namespace="App\Services">
          <metrics complexity="8" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="20" coveredstatements="0" elements="24" coveredelements="0"/>
        </class>
        <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="14" type="stmt" count="0"/>
        <line num="17" type="method" name="getNotifs" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="20" type="stmt" count="0"/>
        <line num="21" type="stmt" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="28" type="method" name="readNotif" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="31" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="39" type="method" name="readAllNotif" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <metrics loc="55" ncloc="55" classes="1" methods="4" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="20" coveredstatements="0" elements="24" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Services/ProfileService.php">
        <class name="App\Services\ProfileService" namespace="App\Services">
          <metrics complexity="18" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="61" coveredstatements="0" elements="68" coveredelements="0"/>
        </class>
        <line num="16" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="22" type="stmt" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="27" type="method" name="getProfile" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="29" type="stmt" count="0"/>
        <line num="32" type="method" name="update" visibility="public" complexity="7" crap="56" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="38" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="56" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="64" type="method" name="getWorks" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="73" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="82" type="method" name="getFavorites" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="96" type="method" name="showContent" visibility="public" complexity="2" crap="6" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="108" type="method" name="removeFavorite" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="112" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="114" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="124" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <metrics loc="131" ncloc="131" classes="1" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="61" coveredstatements="0" elements="68" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Services/ReviewService.php">
        <class name="App\Services\ReviewService" namespace="App\Services">
          <metrics complexity="30" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="130" coveredstatements="0" elements="137" coveredelements="0"/>
        </class>
        <line num="18" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="31" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="36" type="method" name="getContent" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="50" type="method" name="getReviews" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="53" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="55" type="stmt" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="65" type="method" name="countContent" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="70" type="method" name="approval" visibility="public" complexity="11" crap="132" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="94" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="97" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="99" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="101" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="106" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <line num="112" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="114" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="126" type="stmt" count="0"/>
        <line num="127" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="129" type="stmt" count="0"/>
        <line num="131" type="stmt" count="0"/>
        <line num="132" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="135" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="142" type="stmt" count="0"/>
        <line num="144" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <line num="147" type="stmt" count="0"/>
        <line num="148" type="stmt" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="151" type="stmt" count="0"/>
        <line num="155" type="method" name="publication" visibility="public" complexity="9" crap="90" count="0"/>
        <line num="157" type="stmt" count="0"/>
        <line num="159" type="stmt" count="0"/>
        <line num="160" type="stmt" count="0"/>
        <line num="162" type="stmt" count="0"/>
        <line num="163" type="stmt" count="0"/>
        <line num="164" type="stmt" count="0"/>
        <line num="165" type="stmt" count="0"/>
        <line num="166" type="stmt" count="0"/>
        <line num="168" type="stmt" count="0"/>
        <line num="170" type="stmt" count="0"/>
        <line num="171" type="stmt" count="0"/>
        <line num="173" type="stmt" count="0"/>
        <line num="174" type="stmt" count="0"/>
        <line num="175" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="177" type="stmt" count="0"/>
        <line num="178" type="stmt" count="0"/>
        <line num="179" type="stmt" count="0"/>
        <line num="181" type="stmt" count="0"/>
        <line num="182" type="stmt" count="0"/>
        <line num="183" type="stmt" count="0"/>
        <line num="184" type="stmt" count="0"/>
        <line num="185" type="stmt" count="0"/>
        <line num="186" type="stmt" count="0"/>
        <line num="187" type="stmt" count="0"/>
        <line num="189" type="stmt" count="0"/>
        <line num="190" type="stmt" count="0"/>
        <line num="191" type="stmt" count="0"/>
        <line num="192" type="stmt" count="0"/>
        <line num="195" type="stmt" count="0"/>
        <line num="196" type="stmt" count="0"/>
        <line num="197" type="stmt" count="0"/>
        <line num="198" type="stmt" count="0"/>
        <line num="199" type="stmt" count="0"/>
        <line num="200" type="stmt" count="0"/>
        <line num="201" type="stmt" count="0"/>
        <line num="202" type="stmt" count="0"/>
        <line num="204" type="stmt" count="0"/>
        <line num="205" type="stmt" count="0"/>
        <line num="209" type="stmt" count="0"/>
        <line num="210" type="stmt" count="0"/>
        <line num="211" type="stmt" count="0"/>
        <line num="213" type="stmt" count="0"/>
        <line num="214" type="stmt" count="0"/>
        <line num="216" type="stmt" count="0"/>
        <line num="217" type="stmt" count="0"/>
        <line num="218" type="stmt" count="0"/>
        <line num="219" type="stmt" count="0"/>
        <line num="220" type="stmt" count="0"/>
        <metrics loc="223" ncloc="223" classes="1" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="130" coveredstatements="0" elements="137" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Services/RoleService.php">
        <class name="App\Services\RoleService" namespace="App\Services">
          <metrics complexity="19" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="105" coveredstatements="0" elements="112" coveredelements="0"/>
        </class>
        <line num="18" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="26" type="stmt" count="0"/>
        <line num="27" type="stmt" count="0"/>
        <line num="28" type="stmt" count="0"/>
        <line num="31" type="method" name="getAll" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="36" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="55" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="57" type="stmt" count="0"/>
        <line num="58" type="stmt" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="60" type="stmt" count="0"/>
        <line num="61" type="stmt" count="0"/>
        <line num="64" type="method" name="create" visibility="public" complexity="5" crap="30" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="69" type="stmt" count="0"/>
        <line num="70" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="75" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="84" type="stmt" count="0"/>
        <line num="85" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="87" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="89" type="stmt" count="0"/>
        <line num="93" type="method" name="update" visibility="public" complexity="5" crap="30" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="97" type="stmt" count="0"/>
        <line num="98" type="stmt" count="0"/>
        <line num="100" type="stmt" count="0"/>
        <line num="102" type="stmt" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="104" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="110" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <line num="112" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="115" type="stmt" count="0"/>
        <line num="117" type="stmt" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="126" type="method" name="delete" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <line num="131" type="stmt" count="0"/>
        <line num="132" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="135" type="stmt" count="0"/>
        <line num="136" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="140" type="stmt" count="0"/>
        <line num="141" type="stmt" count="0"/>
        <line num="143" type="stmt" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <line num="146" type="stmt" count="0"/>
        <line num="147" type="stmt" count="0"/>
        <line num="148" type="stmt" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="154" type="method" name="getPermission" visibility="public" complexity="3" crap="12" count="0"/>
        <line num="156" type="stmt" count="0"/>
        <line num="157" type="stmt" count="0"/>
        <line num="158" type="stmt" count="0"/>
        <line num="159" type="stmt" count="0"/>
        <line num="160" type="stmt" count="0"/>
        <line num="161" type="stmt" count="0"/>
        <line num="162" type="stmt" count="0"/>
        <line num="163" type="stmt" count="0"/>
        <line num="164" type="stmt" count="0"/>
        <line num="165" type="stmt" count="0"/>
        <line num="166" type="stmt" count="0"/>
        <line num="167" type="stmt" count="0"/>
        <line num="168" type="stmt" count="0"/>
        <line num="170" type="stmt" count="0"/>
        <line num="171" type="stmt" count="0"/>
        <line num="172" type="stmt" count="0"/>
        <line num="174" type="stmt" count="0"/>
        <line num="175" type="stmt" count="0"/>
        <line num="176" type="stmt" count="0"/>
        <line num="177" type="stmt" count="0"/>
        <metrics loc="180" ncloc="176" classes="1" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="105" coveredstatements="0" elements="112" coveredelements="0"/>
      </file>
      <file name="/home/<USER>/grafindo-media-library/app/Services/UserService.php">
        <class name="App\Services\UserService" namespace="App\Services">
          <metrics complexity="22" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="96" coveredstatements="0" elements="103" coveredelements="0"/>
        </class>
        <line num="17" type="method" name="__construct" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="23" type="stmt" count="0"/>
        <line num="24" type="stmt" count="0"/>
        <line num="25" type="stmt" count="0"/>
        <line num="28" type="method" name="getAll" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="30" type="stmt" count="0"/>
        <line num="32" type="stmt" count="0"/>
        <line num="33" type="stmt" count="0"/>
        <line num="34" type="stmt" count="0"/>
        <line num="35" type="stmt" count="0"/>
        <line num="37" type="stmt" count="0"/>
        <line num="39" type="stmt" count="0"/>
        <line num="40" type="stmt" count="0"/>
        <line num="41" type="stmt" count="0"/>
        <line num="42" type="stmt" count="0"/>
        <line num="43" type="stmt" count="0"/>
        <line num="44" type="stmt" count="0"/>
        <line num="45" type="stmt" count="0"/>
        <line num="46" type="stmt" count="0"/>
        <line num="47" type="stmt" count="0"/>
        <line num="48" type="stmt" count="0"/>
        <line num="49" type="stmt" count="0"/>
        <line num="50" type="stmt" count="0"/>
        <line num="51" type="stmt" count="0"/>
        <line num="52" type="stmt" count="0"/>
        <line num="54" type="stmt" count="0"/>
        <line num="57" type="method" name="findOne" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="59" type="stmt" count="0"/>
        <line num="62" type="method" name="create" visibility="public" complexity="6" crap="42" count="0"/>
        <line num="64" type="stmt" count="0"/>
        <line num="66" type="stmt" count="0"/>
        <line num="67" type="stmt" count="0"/>
        <line num="68" type="stmt" count="0"/>
        <line num="71" type="stmt" count="0"/>
        <line num="72" type="stmt" count="0"/>
        <line num="74" type="stmt" count="0"/>
        <line num="76" type="stmt" count="0"/>
        <line num="77" type="stmt" count="0"/>
        <line num="78" type="stmt" count="0"/>
        <line num="79" type="stmt" count="0"/>
        <line num="80" type="stmt" count="0"/>
        <line num="81" type="stmt" count="0"/>
        <line num="82" type="stmt" count="0"/>
        <line num="83" type="stmt" count="0"/>
        <line num="86" type="stmt" count="0"/>
        <line num="88" type="stmt" count="0"/>
        <line num="90" type="stmt" count="0"/>
        <line num="91" type="stmt" count="0"/>
        <line num="92" type="stmt" count="0"/>
        <line num="93" type="stmt" count="0"/>
        <line num="95" type="stmt" count="0"/>
        <line num="96" type="stmt" count="0"/>
        <line num="97" type="stmt" count="0"/>
        <line num="101" type="method" name="update" visibility="public" complexity="8" crap="72" count="0"/>
        <line num="103" type="stmt" count="0"/>
        <line num="105" type="stmt" count="0"/>
        <line num="107" type="stmt" count="0"/>
        <line num="108" type="stmt" count="0"/>
        <line num="109" type="stmt" count="0"/>
        <line num="111" type="stmt" count="0"/>
        <line num="113" type="stmt" count="0"/>
        <line num="114" type="stmt" count="0"/>
        <line num="116" type="stmt" count="0"/>
        <line num="118" type="stmt" count="0"/>
        <line num="119" type="stmt" count="0"/>
        <line num="120" type="stmt" count="0"/>
        <line num="121" type="stmt" count="0"/>
        <line num="122" type="stmt" count="0"/>
        <line num="123" type="stmt" count="0"/>
        <line num="124" type="stmt" count="0"/>
        <line num="125" type="stmt" count="0"/>
        <line num="128" type="stmt" count="0"/>
        <line num="130" type="stmt" count="0"/>
        <line num="132" type="stmt" count="0"/>
        <line num="133" type="stmt" count="0"/>
        <line num="134" type="stmt" count="0"/>
        <line num="135" type="stmt" count="0"/>
        <line num="137" type="stmt" count="0"/>
        <line num="138" type="stmt" count="0"/>
        <line num="139" type="stmt" count="0"/>
        <line num="143" type="method" name="delete" visibility="public" complexity="4" crap="20" count="0"/>
        <line num="145" type="stmt" count="0"/>
        <line num="147" type="stmt" count="0"/>
        <line num="149" type="stmt" count="0"/>
        <line num="150" type="stmt" count="0"/>
        <line num="151" type="stmt" count="0"/>
        <line num="152" type="stmt" count="0"/>
        <line num="153" type="stmt" count="0"/>
        <line num="154" type="stmt" count="0"/>
        <line num="155" type="stmt" count="0"/>
        <line num="156" type="stmt" count="0"/>
        <line num="157" type="stmt" count="0"/>
        <line num="160" type="stmt" count="0"/>
        <line num="162" type="stmt" count="0"/>
        <line num="163" type="stmt" count="0"/>
        <line num="166" type="stmt" count="0"/>
        <line num="167" type="stmt" count="0"/>
        <line num="168" type="stmt" count="0"/>
        <line num="169" type="stmt" count="0"/>
        <line num="170" type="stmt" count="0"/>
        <line num="171" type="stmt" count="0"/>
        <line num="175" type="method" name="getRoles" visibility="public" complexity="1" crap="2" count="0"/>
        <line num="177" type="stmt" count="0"/>
        <metrics loc="179" ncloc="175" classes="1" methods="7" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="96" coveredstatements="0" elements="103" coveredelements="0"/>
      </file>
    </package>
    <metrics files="75" loc="4902" ncloc="4718" classes="56" methods="268" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="1958" coveredstatements="17" elements="2226" coveredelements="18"/>
  </project>
</coverage>
