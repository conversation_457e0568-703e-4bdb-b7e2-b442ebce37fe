@extends('layouts.partials.main')

@section('title', 'Submit Content')

@section('content')
{{-- Head Content --}}
@include('pages.submit-content.components.header')
{{-- End of head content --}}
@include('pages.submit-content.components.toolbar')


{{-- Submit Tabs --}}
@canAccess('uploads.not_submitted')
<div class="transition-opacity duration-700" id="tab_draft">
@include('pages.submit-content.tabs.draft')
</div>
@endcanAccess
@canAccess('uploads.pending')
<div class="hidden transition-opacity duration-700" id="tab_pending">
@include('pages.submit-content.tabs.pending')
</div>
@endcanAccess
{{-- <div class="hidden transition-opacity duration-700" id="tab_reviewed">
@include('pages.submit-content.tabs.reviewed')
</div> --}}
@canAccess('uploads.rejected')
<div class="hidden transition-opacity duration-700" id="tab_rejected">
@include('pages.submit-content.tabs.rejected')
</div>
@endcanAccess
@canAccess('uploads.approved')
<div class="hidden transition-opacity duration-700" id="tab_approved">
@include('pages.submit-content.tabs.approved')
</div>
@endcanAccess
@canAccess('uploads.published')
<div class="hidden transition-opacity duration-700" id="tab_published">
@include('pages.submit-content.tabs.published')
</div>
@endcanAccess
{{-- End of submit tabs --}}

{{-- Upload Drpozone Modal --}}
@include('pages.submit-content.dropzone')
{{-- End of dropzone modal --}}

{{-- Modal View Doc Release --}}
@include('pages.submit-content.view-doc')
{{-- End of view doc release --}}
@endsection

@push('scripts')
@include('pages.submit-content.components.scripts')

@include('pages.submit-content.components.main-scripts')

@include('pages.submit-content.components.content-scripts')

@include('pages.submit-content.components.form-scripts')

@include('pages.submit-content.components.review-scripts')
@include('pages.submit-content.components.delete-scripts')
@endpush