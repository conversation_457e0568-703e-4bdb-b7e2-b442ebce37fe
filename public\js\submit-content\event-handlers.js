/**
 * Submit Content Event Handlers Module
 * Handles all event listeners and user interactions
 */

window.SubmitContent = window.SubmitContent || {};

window.SubmitContent.EventHandlers = {
  
  /**
   * Initialize all event handlers
   */
  init: function() {
    this.bindTabEvents();
    this.bindFilterEvents();
    this.bindFlagEvents();
    this.bindCardEvents();
    this.bindButtonEvents();
    this.bindWindowEvents();
    this.bindSidebarEvents();
  },

  /**
   * Bind tab click events
   */
  bindTabEvents: function() {
    $(document).on("click", ".tab", function(){
      const state = window.SubmitContent.state;
      const config = window.SubmitContent.config;
      
      console.log('Tab clicked:', $(this).data('status'));
      window.SubmitContent.UIManager.switchTab(this);
      
      // Get content with current status and filter
      window.SubmitContent.ContentManager.getContent(state.status, state.filter, state.authorId);
      
      // Update URL without reloading the page
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set('tab', config.statusUrlMap[state.status]);
      window.history.pushState({}, '', newUrl);
      
      // Update sidebar menu active state
      $("#submit-content-submenu .menu-link").removeClass("bg-gray-50 dark:bg-coal-300");
      $(`#submit-content-submenu a[href*="tab=${config.statusUrlMap[state.status]}"]`).addClass("bg-gray-50 dark:bg-coal-300");
    });
  },

  /**
   * Bind filter click events
   */
  bindFilterEvents: function() {
    $(document).on("click", ".filter-media", function(){
      const state = window.SubmitContent.state;
      
      state.filter = $(this).data("filter");
      state.selectedIds = [];
      state.detailContent.html(state.emptyContent);
      state.btnUpdate.addClass("hidden").removeClass("flex");
      $(".filter-media").removeClass("active");
      $(this).addClass("active");

      // Update filter
      window.SubmitContent.ContentManager.updateFilter(state.filter, state.status);

      // Get content
      window.SubmitContent.ContentManager.getContent(state.status, state.filter, state.authorId);

      // Update URL without reloading the page
      window.SubmitContent.UIManager.updateURL({ filter: state.filter });
    });
  },

  /**
   * Bind flag click events
   */
  bindFlagEvents: function() {
    $(document).on("click", ".flag", function(){
      const state = window.SubmitContent.state;
      
      state.flag = $(this).data("flag");
      state.selectedIds = [];
      state.detailContent.html(state.emptyContent);
      state.btnUpdate.addClass("hidden").removeClass("flex");
      $(".flag").removeClass("active");
      $(this).addClass("active");
      window.SubmitContent.ContentManager.updateFlag(state.flag, state.filter);
      window.SubmitContent.ContentManager.getContent(state.flag, state.filter, state.authorId);
    });
  },

  /**
   * Bind card selection events
   */
  bindCardEvents: function() {
    // Event: Klik pada card untuk menampilkan content details
    $(document).on("click", ".select-card", function (e) {
      const state = window.SubmitContent.state;
      let card = $(this);
      let cardId = card.data("id");

      // Jika klik pada checkbox atau area sekitarnya, jangan lakukan apa-apa
      // Karena akan ditangani oleh handler checkbox
      if ($(e.target).closest('.multiple-select').length > 0 ||
          $(e.target).hasClass('unchecked') ||
          $(e.target).hasClass('checked')) {
          return;
      }

      // Jika dalam mode multiple select, klik pada card tidak akan mengubah seleksi checkbox
      // Hanya tampilkan detail content
      if (state.isMultipleSelect) {
          // Hanya tampilkan detail content tanpa mengubah seleksi
          state.detailContent.html(state.emptyContent);
          return;
      }

      // Jika klik di area card, hanya tampilkan detail tanpa mengubah checkbox
      window.SubmitContent.SelectionManager.handleShowDetails(card, cardId);
    });

    // Event: Klik pada checkbox multiple-select
    $(document).on("click", ".multiple-select, .unchecked, .checked", function (e) {
      e.stopPropagation(); // Hindari trigger klik pada card
      e.preventDefault(); // Prevent default behavior

      window.SubmitContent.SelectionManager.handleCheckboxClick(this);
    });
  },

  /**
   * Bind button events
   */
  bindButtonEvents: function() {
    const state = window.SubmitContent.state;
    
    // Event: Klik pada tombol delete
    $(document).on("click", ".btn-delete", function (e) {
      e.preventDefault();
      let contentId = $(this).data("id");
      deleteContent(contentId);
    });

    // Bulk delete button
    $('#btn-bulk-delete').on('click', function(e){
      e.preventDefault();
      bulkDelete(state.selectedIds);
    });

    // Close banner button
    $('#btn-close-banner').on('click', function(){
      window.SubmitContent.SelectionManager.clearSelections();
    });

    // Save button
    $('#btn-save').on('click', function(e){
      Swal.fire({
        title: "Are you sure?",
        text: "Your data will saved as draft.",
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, save it!"
      }).then((result) => {
        if (result.isConfirmed) {
          $('#update-type').val('update');
          state.btnLoading.addClass("flex").removeClass("hidden");
          state.btnUpdate.removeClass("flex").addClass("hidden");
          state.detailContentForm.submit();
        }
      });
    });

    // Submit button
    $('#btn-submit').on('click', function(){
      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        customClass:{
          confirmButton: 'btn btn-light',
          cancelButton: 'btn btn-danger',
        },
        confirmButtonText: "Yes, submit it!"
      }).then((result) => {
        if (result.isConfirmed) {
          $('#update-type').val('submit');
          state.btnLoading.addClass("flex").removeClass("hidden");
          state.btnUpdate.removeClass("flex").addClass("hidden");
          state.detailContentForm.submit();
        }
      });
    });
  },

  /**
   * Bind window events
   */
  bindWindowEvents: function() {
    const state = window.SubmitContent.state;
    
    // Add window resize event to adjust banner position
    $(window).on('resize', function() {
      if (state.isMultipleSelect) {
        window.SubmitContent.UIManager.adjustBannerPosition();
      }
      state.smScreen = window.matchMedia("(max-width: 991px)").matches;
    });
  },

  /**
   * Bind sidebar navigation events
   */
  bindSidebarEvents: function() {
    const state = window.SubmitContent.state;
    const config = window.SubmitContent.config;
    
    // Handle sidebar navigation clicks
    $('#submit-content-submenu a').on('click', function(e) {
      console.log('--- Sidebar menu item clicked ---');
      
      // Only handle clicks if we're already on the submit-content page
      if (!window.location.pathname.includes('/submit-content')) {
        return; // Let the link work normally if not on submit-content page
      }
      
      e.preventDefault();

      // Get the tab parameter from the URL
      const href = $(this).attr('href');
      const url = new URL(href, window.location.origin);
      const tabParam = url.searchParams.get('tab');
      console.log('Tab parameter from URL:', tabParam);

      // Find and click the corresponding tab
      if (tabParam && config.tabStatusMap[tabParam]) {
        const newStatus = config.tabStatusMap[tabParam];
        console.log('Switching to status:', newStatus);
        
        // Find the corresponding tab element
        const tabElement = document.querySelector(`[data-status="${newStatus}"]`);
        if (!tabElement) {
          console.error('Tab element not found for status:', newStatus);
          return;
        }
        
        // Update the tab and UI state
        window.SubmitContent.UIManager.switchTab(tabElement);
        
        // Update URL with new tab and current filter
        window.SubmitContent.UIManager.updateURL({ 
          tab: tabParam, 
          filter: state.filter 
        });
        
        // Get the content for the new tab
        window.SubmitContent.ContentManager.getContent(newStatus, state.filter, state.authorId);
        
        // Update sidebar menu active state
        $("#submit-content-submenu .menu-link").removeClass("bg-gray-50 dark:bg-coal-300");
        $(this).addClass("bg-gray-50 dark:bg-coal-300");
      }
    });
  }
};
