FROM docker.io/dunglas/frankenphp:php8.2.27
ENV TZ="Asia/Jakarta"
ENV COMPOSER_ALLOW_SUPERUSER=1
ENV PHP_INI_SCAN_DIR=":$PHP_INI_DIR/app.conf.d"

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

COPY --from=ghcr.io/composer/docker:lts /usr/bin/composer /usr/bin/composer
COPY docker/10-custom.ini $PHP_INI_DIR/app.conf.d/
COPY docker/Caddyfile /etc/caddy/Caddyfile

RUN apt update && apt-get install -y --no-install-recommends \
  libpng-dev libjpeg-dev libwebp-dev libfreetype6-dev zlib1g-dev \
  && rm -rf /var/lib/apt/lists/*

RUN docker-php-ext-configure gd --enable-gd --with-freetype --with-jpeg --with-webp
RUN install-php-extensions \
  intl mbstring exif pcntl bcmath gd zip opcache redis pdo pdo_mysql mysqli

RUN apt update && apt install libmagickwand-dev -y \
  && pecl install imagick \
  && docker-php-ext-enable imagick