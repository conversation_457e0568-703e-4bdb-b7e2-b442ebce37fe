ARG CI_REGISTRY_IMAGE=changeme

ARG BUILD_VERSION=changeme
ARG APP_URL=changeme

FROM docker.io/composer:lts AS vendor
WORKDIR /app

COPY composer.* ./
RUN composer install \
  --no-interaction \
  --no-plugins \
  --no-scripts \
  --no-dev \
  --ignore-platform-reqs \
  --prefer-dist \
  --optimize-autoloader

FROM node:20.18-alpine AS frontend
WORKDIR /app

ARG BUILD_VERSION=changeme
ARG APP_URL=changeme
ENV APP_URL=$APP_URL
ENV VITE_APP_NAME=$APP_URL

COPY . .

RUN npm install
RUN npm run build

FROM $CI_REGISTRY_IMAGE:base AS release
WORKDIR /app
ENV TZ="Asia/Jakarta"

ARG BUILD_VERSION=changeme
ARG APP_URL=changeme
ENV APP_URL=$APP_URL
ENV VITE_APP_NAME=$APP_URL

COPY . .

COPY --from=vendor /app/vendor/ /app/vendor/
COPY --from=frontend /app/public/ /app/public/

RUN php artisan storage:link