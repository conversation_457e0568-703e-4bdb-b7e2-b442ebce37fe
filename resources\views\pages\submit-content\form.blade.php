<div class="card">
  <div class="card-header">
    <h3 class="text-lg">Content Details</h3>
  </div>
  <div class="card-body bg-[--tw-navbar-bg] scrollable min-h-[70vh]">
    <form action="{{ url('submit-content/update') }}" method="POST" id="detail-content-form" enctype="multipart/form-data">
      @method('PUT')
      @csrf

      <label for="filename" class="text-sm">Asset Id</label>
      <h3 class="mb-4 font-semibold" id="filename-draft"></h3>

      <div class="mb-4">
        <label for="description" class="text-sm mb-3">Deskripsi<span class="text-danger">*</span></label>
        <textarea class="textarea @error('description') ? border-danger : '' @enderror" type="text" id="description-draft" rows="3" name="description" required></textarea>
        <div class="flex justify-start w-full">
          <span id="description_warning" class="text-xs mt-1 text-red-600 hidden">Deskripsi minimal 2 kata</span>
        </div>
        @error('description')
          <span class="text-danger text-sm">{{ $message }}</span>
        @enderror
      </div>

      <div class="mb-4">
        <label for="keyword" class="text-sm mb-3">Kata Kunci<span class="text-danger">*</span></label>
        <div class="relative">
            <input type="text" id="keywordInput" class="input @error('keyword') ? border-danger : '' @enderror" placeholder="Ketik kata kunci dan tekan Enter (pisahkan dengan koma)" autocomplete="off" required>
            <!-- Dropdown removed to disable autofill suggestions -->
        </div>
        <div class="flex justify-between w-full" id="key_span">
          <div class="flex items-center gap-2">
            <span id="key_warning" class="text-xs mt-1 text-red-600 hidden">Kata kunci minimal 5 kata</span>
            <span class="text-xs mt-1"><span id="keywords_counter">0</span>/20 Kata Kunci</span>
          </div>
        </div>
        <div id="keywordsContainerDraft" class="mt-2 flex flex-wrap gap-2"></div>
        <input type="hidden" name="keyword" id="hiddenKeywords">
        @error('keyword')
          <span class="text-danger text-sm">{{ $message }}</span>
        @enderror
      </div>

      <div class="mb-4">
        <label for="category" class="text-sm mb-3">Kategori 1 - Umum<span class="text-danger">*</span></label>
        <select class="select" id="category-draft" name="category_id">
          <option value="">Pilih Kategori</option>
          @foreach ($general_categories as $item)
          <option value="{{ $item->id }}">{{ $item->name }}</option>
          @endforeach
        </select>
        @error('category_id')
          <span class="text-danger text-sm">{{ $message }}</span>
        @enderror
      </div>

      <div class="mb-4">
        <label for="category_2" class="text-sm mb-3">Kategori 2 - Mata Pelajaran (Opsional)</label>
        <select class="select" id="category_2-draft" name="category_2_id">
          <option value="">Pilih Kategori</option>
          @foreach ($subject_categories as $item)
          <option value="{{ $item->id }}">{{ $item->name }}</option>
          @endforeach
        </select>
        @error('category_2_id')
          <span class="text-danger text-sm">{{ $message }}</span>
        @enderror
      </div>

      <div class="mb-4">
        <label for="media-type" class="text-sm mb-3">Tipe Media<span class="text-danger">*</span></label>
        <select class="select" id="media_type-draft" name="media_type">
          <option value="Photo">Photo</option>
          <option value="Illustration">Illustration</option>
          <option value="Video">Video</option>
          <option value="Audio">Audio</option>
          <option value="Document">Document</option>
        </select>
        @error('media_type')
          <span class="text-danger text-sm">{{ $message }}</span>
        @enderror
      </div>

      <div class="mb-4">
        <label for="creator" class="text-sm mb-3">Kreator<span class="text-danger">*</span></label>
        <input type="text" class="input @error('creator_name') ? border-danger : '' @enderror" placeholder="Masukan nama kreator" id="creator_name-draft" name="creator_name" required/>
        @error('creator_name')
          <span class="text-danger text-sm">{{ $message }}</span>
        @enderror
      </div>

      <div class="mb-4">
        <label for="date_taken" class="text-sm mb-3">Tanggal Pengambilan Konten<span class="text-danger">*</span></label>
        <div class="input-group">
          <input class="input @error('date_taken') ? border-danger : '' @enderror" placeholder="" type="date" max="{{ date('Y-m-d') }}" id="date_taken-draft" name="date_taken" data-datepicker required/>
        </div>
        @error('date_taken')
          <span class="text-danger text-sm">{{ $message }}</span>
        @enderror
      </div>

      <div class="mb-4">
        <label for="release_document" class="text-sm mb-3">Dokumen Rilis (Opsional)</label>
        <div class="input-group mb-2">
          <input class="file-input @error('release_document') ? border-danger : '' @enderror" placeholder="" accept=".pdf, .doc, .docx" type="file" id="release_document-draft" name="release_document" required/>
        </div>
        <span id="current_release_doc-draft" class="text-sm"></span>
        @error('release_document')
          <span class="text-danger text-sm">{{ $message }}</span>
        @enderror
      </div>

      <div id="group-input-content-id"></div>
      <input type="hidden" id="update-type" name="update_type">
    </form>
  </div>
</div>
