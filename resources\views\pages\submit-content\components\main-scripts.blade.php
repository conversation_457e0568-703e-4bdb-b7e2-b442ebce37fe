<!-- Include modular JavaScript files -->
<script src="{{ asset('js/submit-content/config.js') }}"></script>
<script src="{{ asset('js/submit-content/content-manager.js') }}"></script>
<script src="{{ asset('js/submit-content/ui-manager.js') }}"></script>
<script src="{{ asset('js/submit-content/selection-manager.js') }}"></script>
<script src="{{ asset('js/submit-content/event-handlers.js') }}"></script>
<script src="{{ asset('js/submit-content/main.js') }}"></script>

<script>
  // Pass Laravel data to JavaScript
  window.SubmitContentConfig = window.SubmitContentConfig || {};
  window.SubmitContentConfig.authorId = "{{ auth()->user()->id }}";
  window.SubmitContentConfig.routes = {
    getContent: "{{ route('get-content-author') }}"
  };
  window.SubmitContentConfig.templates = {
    formContent: `@include('pages.submit-content.form')`,
    showContent: `@include('pages.submit-content.show')`,
    emptyContent: `@include('pages.submit-content.empty')`
  };
</script>