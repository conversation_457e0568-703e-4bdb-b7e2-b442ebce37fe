/**
 * Submit Content Configuration Module
 * Handles global variables and configuration settings
 */

// Global variables
window.SubmitContent = window.SubmitContent || {};

// Initialize global state
window.SubmitContent.state = {
  // DOM elements
  tabsEl: null,
  tabs: null,
  pageTitle: null,
  selectionBanner: null,
  btnUpdate: null,
  btnLoading: null,
  detailContent: null,
  detailContentForm: null,
  inputContentId: null,
  
  // Configuration
  authorId: null,
  keys: ["draft", "pending", "reviewed", "published", "approved", "rejected"],
  smScreen: false,
  
  // Current state
  status: "Draft",
  filter: "index",
  flag: "Reviewed",
  
  // Data
  contentCounts: {
    draft: { index: 0, image: 0, video: 0, audio: 0, document: 0 },
    pending: { index: 0, image: 0, video: 0, audio: 0, document: 0 },
    reviewed: { index: 0, image: 0, video: 0, audio: 0, document: 0 },
    approved: { index: 0, image: 0, video: 0, audio: 0, document: 0 },
    rejected: { index: 0, image: 0, video: 0, audio: 0, document: 0 },
    published: { index: 0, image: 0, video: 0, audio: 0, document: 0 }
  },
  selectedIds: [],
  selectedKeywords: [],
  isMultipleSelect: false,
  descriptionCounter: null,
  itemData: {},
  
  // Templates
  formContent: '',
  showContent: '',
  emptyContent: ''
};

// Configuration maps
window.SubmitContent.config = {
  tabStatusMap: {
    'not_submitted': 'Draft',
    'pending': 'Pending',
    'rejected': 'Rejected',
    'approved': 'Approved',
    'published': 'Published'
  },
  
  statusUrlMap: {
    'Draft': 'not_submitted',
    'Pending': 'pending',
    'Rejected': 'rejected',
    'Approved': 'approved',
    'Published': 'published'
  },
  
  statusTitleMap: {
    'Draft': 'Not Submitted',
    'Pending': 'Pending',
    'Rejected': 'Rejected',
    'Approved': 'Approved',
    'Published': 'Published'
  },
  
  badgeClassMap: {
    'Draft': 'badge-outline badge-dark',
    'Pending': 'badge-outline badge-warning',
    'Rejected': 'badge-outline badge-danger',
    'Approved': 'badge-outline badge-primary',
    'Published': 'badge-outline badge-success'
  },
  
  filterMap: {
    index: { name: "Semua", icon: "fa-list" },
    image: { name: "Image", icon: "fa-image" },
    video: { name: "Video", icon: "fa-video" },
    audio: { name: "Audio", icon: "fa-music" },
    document: { name: "Document", icon: "fa-file" }
  },
  
  pageNote: {
    draft: { note: "Need To Be Submitted"},
    pending: { note: "Waiting To Be Reviewed"},
    reviewed: { note: "Have Been Reviewed"},
    rejected: { note: "Have Been Rejected"},
    approved: { note: "Have Been Approved"},
    published: { note: "Are Published"}
  },
  
  flagMap: {
    reviewed: { name: "Semua" },
    approved: { name: "Approved" },
    rejected: { name: "Rejected" }
  }
};

// Initialize configuration from Laravel data
window.SubmitContent.initConfig = function() {
  if (window.SubmitContentConfig) {
    window.SubmitContent.state.authorId = window.SubmitContentConfig.authorId;
    window.SubmitContent.routes = window.SubmitContentConfig.routes;
    
    if (window.SubmitContentConfig.templates) {
      window.SubmitContent.state.formContent = window.SubmitContentConfig.templates.formContent;
      window.SubmitContent.state.showContent = window.SubmitContentConfig.templates.showContent;
      window.SubmitContent.state.emptyContent = window.SubmitContentConfig.templates.emptyContent;
    }
  }
};
