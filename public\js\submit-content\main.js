/**
 * Submit Content Main Module
 * Main initialization and orchestration
 */

window.SubmitContent = window.SubmitContent || {};

window.SubmitContent.Main = {
  
  /**
   * Initialize the application
   */
  init: function() {
    // Initialize configuration from Laravel data
    window.SubmitContent.initConfig();
    
    const state = window.SubmitContent.state;
    const config = window.SubmitContent.config;
    
    // Initialize DOM elements
    this.initDOMElements();
    
    // Initialize URL parameters and state
    this.initURLState();
    
    // Initialize UI components
    this.initUIComponents();
    
    // Initialize event handlers
    window.SubmitContent.EventHandlers.init();
    
    // Load initial content
    window.SubmitContent.ContentManager.getContent(state.status, state.filter, state.authorId);
  },

  /**
   * Initialize DOM elements
   */
  initDOMElements: function() {
    const state = window.SubmitContent.state;
    
    state.tabsEl = document.querySelector('#my_tabs');
    state.tabs = new KTTabs(state.tabsEl, { hiddenClass: 'hidden' });
    state.pageTitle = $("#page-title");
    state.selectionBanner = $("#banner-container");
    state.smScreen = window.matchMedia("(max-width: 991px)").matches;
    state.btnUpdate = $("#group-btn-update");
    state.btnLoading = $(".btn-loading");
    state.detailContent = $("#detail-" + state.status.toLowerCase());
  },

  /**
   * Initialize URL state from parameters
   */
  initURLState: function() {
    const state = window.SubmitContent.state;
    const config = window.SubmitContent.config;
    
    // Get tab and filter from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');
    const filterParam = urlParams.get('filter');

    // Set initial status based on URL parameter or default to Draft
    state.status = tabParam && config.tabStatusMap[tabParam] ? config.tabStatusMap[tabParam] : "Draft";

    // Set initial filter based on URL parameter or default to index
    state.filter = filterParam || "index";

    state.flag = "Reviewed";
    state.detailContent = $("#detail-" + state.status.toLowerCase());
  },

  /**
   * Initialize UI components
   */
  initUIComponents: function() {
    const state = window.SubmitContent.state;
    const config = window.SubmitContent.config;
    
    // Find and activate the correct tab based on status
    if (state.status !== "Draft") {
      const tabToActivate = document.querySelector(`[data-status="${state.status}"]`);
      if (tabToActivate) {
        state.tabs.show(tabToActivate);
      }
    }

    // Activate the correct filter
    $(".filter-media").removeClass("active");
    $(`.filter-media[data-filter="${state.filter}"]`).addClass("active");

    // Update page title and badge based on status
    window.SubmitContent.UIManager.updatePageTitle(state.status);

    // Make sure the badge is visible with the default count of 0
    $("#status-badge").text("0");
    $("#status-badge").removeClass('hidden');

    // Initialize the "Upload Now" button visibility
    if (state.status !== "Draft") {
      $("#upload-now-button").hide();
    }

    // Initialize banner position
    window.SubmitContent.UIManager.adjustBannerPosition();

    // Initialize sidebar menu active state
    this.initSidebarState();
  },

  /**
   * Initialize sidebar active state
   */
  initSidebarState: function() {
    const state = window.SubmitContent.state;
    const config = window.SubmitContent.config;
    
    // Update sidebar menu active state on page load
    // First, remove all active classes from all submenu items
    $("#submit-content-submenu .menu-link").removeClass("bg-gray-50 dark:bg-coal-300 bg-gray-800");

    // Add active class to the current tab's menu item
    const currentTabParam = config.statusUrlMap[state.status];
    $(`#submit-content-submenu a[href*="tab=${currentTabParam}"]`).addClass("bg-gray-50 dark:bg-coal-300");

    // Add custom CSS to override the bullet point
    if (!$("#sidebar-bullet-fix").length) {
      $("head").append(`
        <style id="sidebar-bullet-fix">
          #submit-content-submenu .menu-link::before {
            opacity: 0 !important;
          }
          #submit-content-submenu .menu-link.bg-gray-50::before {
            opacity: 1 !important;
          }
        </style>
      `);
    }
  }
};

// Initialize when document is ready
$(document).ready(function() {
  window.SubmitContent.Main.init();
});
