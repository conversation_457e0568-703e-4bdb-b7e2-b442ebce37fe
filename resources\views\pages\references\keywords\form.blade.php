@extends('layouts.partials.main')

@section('title', 'Form Keyword')

@section('content')
{{-- Head Content --}}
<div class="mb-5 lg:mb-7.5">
  <!-- Container -->
  <div class="container-fixed flex items-center justify-between flex-wrap gap-5">
   <div class="flex flex-col justify-center items-start flex-wrap gap-1 lg:gap-2">
    <h1 class="font-medium text-lg text-gray-900">
     {{ @$title }} Keyword
    </h1>
    <div class="flex items-center gap-1 text-sm font-normal">
     <span class="text-gray-700">References</span>
     <span class="text-gray-400 text-sm"> / </span>
     <a class="text-gray-700 hover:text-primary" href="{{ route('keywords') }}">{{ @$page }}</a>
      <span class="text-gray-400 text-sm"> / </span>
      <span class="text-gray-700">{{ @$title }}</span>
    </div>
   </div>
  </div>
  <!-- End of Container -->
</div>
{{-- End of head content --}}
{{-- Body content --}}
<div class="container-fixed">
    <form action="{{ @$title == 'Edit' ? url('reference/keywords/'.@$data->id.'/update') : route('keywords-store') }}" method="POST" enctype="multipart/form-data">
      @if(@$title == 'Edit')
        @method('PUT')
      @endif
      @csrf
      <div class="card mb-5">
        <div class="card-header">
         <h3 class="card-title">
          Form {{ @$title }}
         </h3>
        </div>
        <div class="card-body">
          <div class="grid gap-5">

            <div class="">
              <label for="name" class="text-sm">Name<span class="text-danger">*</span></label>
              <div class="input-group mt-2">
                <span class="btn btn-icon btn-icon-lg btn-input">
                <i class="fa-solid fa-font"></i>
                </span>
                <input class="input @error('name') ? border-danger : '' @enderror" placeholder="Name" type="text" name="name" value="{{ old('name', $data->name ?? '') }}" required/>
              </div>
              @error('name')
                <span class="text-danger text-sm">{{ $message }}</span>
              @enderror
            </div>
          
          </div>
        </div>
      </div>
      <div class="flex justify-center lg:justify-end mt-2 mb-4 gap-3">
        <button class="btn btn-light" type="submit">Save Changes</button>
        <a href="{{ route('keywords') }}" class="btn btn-danger btn-outline">Cancel</a>
      </div>
    </form>
</div>
{{-- End of body content --}}
@endsection