/**
 * Submit Content Manager Module
 * Handles content loading and data management
 */

window.SubmitContent = window.SubmitContent || {};

window.SubmitContent.ContentManager = {
  
  /**
   * Get content from server
   */
  getContent: function(status, mediaType, author, url) {
    const state = window.SubmitContent.state;
    const config = window.SubmitContent.config;
    
    // Make sure the badge is visible with the current status color before the AJAX call
    window.SubmitContent.UIManager.updatePageTitle(status);

    $.ajax({
      url: url || window.SubmitContent.routes.getContent,
      type: "GET",
      data: {
        'status': status,
        'media_type': mediaType,
        'author': author
      },
      beforeSend: function() {
        $(".loading-content").show();
        $(".toolbar-skeleton").show();
        $("#filter-menu").hide();
        $("#tab-note").hide();
        $("#flag-menu").hide();
      },
      success: function(response) {
        let count = response.data.count;

        // Log the count data for debugging
        console.log("Count data:", count);

        // Update counts for all statuses
        state.keys.forEach(key => {
          // Convert to lowercase for consistency
          const statusKey = key.toLowerCase();

          // Get the count values, defaulting to 0 if not present
          state.contentCounts[statusKey].index = parseInt(count[statusKey] || 0);
          state.contentCounts[statusKey].image = parseInt(count[`${statusKey}_image`] || 0);
          state.contentCounts[statusKey].video = parseInt(count[`${statusKey}_video`] || 0);
          state.contentCounts[statusKey].audio = parseInt(count[`${statusKey}_audio`] || 0);
          state.contentCounts[statusKey].document = parseInt(count[`${statusKey}_document`] || 0);

          console.log(`Status: ${statusKey}, Count: ${state.contentCounts[statusKey].index}`);
        });

        // Special case: For Approved tab, include Published content count
        // This is because in the Approved tab, we show both Approved and Published content
        if (status === "Approved") {
          state.contentCounts.approved.index = parseInt(count.approved || 0) + parseInt(count.published || 0);
          state.contentCounts.approved.image = parseInt(count.approved_image || 0) + parseInt(count.published_image || 0);
          state.contentCounts.approved.video = parseInt(count.approved_video || 0) + parseInt(count.published_video || 0);
          state.contentCounts.approved.audio = parseInt(count.approved_audio || 0) + parseInt(count.published_audio || 0);
          state.contentCounts.approved.document = parseInt(count.approved_document || 0) + parseInt(count.published_document || 0);

          console.log("Updated Approved count:", state.contentCounts.approved.index);
        }

        // Update the page title and badge immediately after getting the counts
        window.SubmitContent.UIManager.updatePageTitle(status);

        // Log the current status count for debugging
        console.log("Current status count:", state.contentCounts[status.toLowerCase()].index);

        $("#tab-note").show();
        $("#filter-menu").show();
        $(".toolbar-skeleton").hide();
        $(".loading-content").hide();
        contentData(response.data.content);

        // Always update filter for all statuses
        window.SubmitContent.ContentManager.updateFilter(state.filter, status);

        // if(["Reviewed", "Approved", "Rejected"].includes(status)){
        if(["Reviewed"].includes(status)){
          $("#flag-menu").show();
          window.SubmitContent.ContentManager.updateFlag(status, state.filter);
        }
      },
      error: function(status, error) {
        console.log("Error:", status, error);
      }
    });
  },

  /**
   * Update filter display and counts
   */
  updateFilter: function(filter, status) {
    const state = window.SubmitContent.state;
    const config = window.SubmitContent.config;
    
    let statusKey = status.toLowerCase();
    let filterKey = filter.toLowerCase();

    // Make sure contentCounts[statusKey] exists
    if (!state.contentCounts[statusKey]) {
      state.contentCounts[statusKey] = { index: 0, image: 0, video: 0, audio: 0, document: 0 };
    }

    let count = state.contentCounts[statusKey][filterKey] ?? 0;

    $("#btn-filter").html(`
      <span class="flex items-center me-1">
       <i class="fa-solid ${config.filterMap[filterKey].icon}"></i>
      </span>
      <span class="hidden md:inline text-nowrap">
       ${config.filterMap[filterKey].name} (${count})
      </span>
      <span class="inline md:hidden text-nowrap">
       ${config.filterMap[filterKey].name} (${count})
      </span>
      <span class="flex items-center lg:ms-4">
       <i class="ki-filled ki-down !text-xs"></i>
      </span>
    `);

    ["index", "image", "video", "audio", "document"].forEach(media => {
      let mediaCount = state.contentCounts[statusKey][media] ?? "0";
      $(`#filter-item-${media}`).text(`${config.filterMap[media].name} (${mediaCount})`);
    });

    state.keys.forEach(key => {
      // Only show count if greater than 0
      const count = parseInt(state.contentCounts[key].index) || 0;
      if (count > 0) {
        $(`#tab-item-${key}`).text(count);
      } else {
        $(`#tab-item-${key}`).text("");
      }
    });

    // Get count for current status
    const statusCount = parseInt(state.contentCounts[statusKey].index) || 0;

    // Update the tab note with count
    if (statusCount > 0) {
      $('#tab-note').text(statusCount + " File(s) " + config.pageNote[statusKey].note);
    } else {
      $('#tab-note').text("0 File(s) " + config.pageNote[statusKey].note);
    }

    // Update the page title and badge when counts are updated
    window.SubmitContent.UIManager.updatePageTitle(status);
  },

  /**
   * Update flag display and counts
   */
  updateFlag: function(flag, filter) {
    const state = window.SubmitContent.state;
    const config = window.SubmitContent.config;
    
    let flagKey = flag.toLowerCase();
    let filterKey = filter.toLowerCase();

    // Make sure contentCounts[flagKey] exists
    if (!state.contentCounts[flagKey]) {
      state.contentCounts[flagKey] = { index: 0, image: 0, video: 0, audio: 0, document: 0 };
    }

    let count = state.contentCounts[flagKey][filterKey] ?? 0;

    $("#btn-flag").html(`
      <span class="hidden md:inline text-nowrap">
        ${config.flagMap[flagKey].name} (${count})
      </span>
      <span class="inline md:hidden text-nowrap">
        ${config.flagMap[flagKey].name} (${count})
      </span>
      <span class="flex items-center lg:ms-4">
        <i class="ki-filled ki-down !text-xs"></i>
      </span>
    `);

    ["reviewed", "approved", "rejected"].forEach(media => {
      let mediaCount = state.contentCounts[media][filterKey] ?? "0";
      $(`#flag-item-${media}`).text(`${config.flagMap[media].name} (${mediaCount})`);
    });
  }
};
