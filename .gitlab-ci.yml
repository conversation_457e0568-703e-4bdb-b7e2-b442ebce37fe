stages:
- test
- code-quality
- base-build
- build
- deploy
- migrate
- seed

workflow:
  rules:
  - if: $CI_COMMIT_BRANCH
    variables:
      STAGE: development
      IMAGE_TAG: $CI_REGISTRY_IMAGE:dev.$CI_COMMIT_SHORT_SHA
      LATEST_IMAGE_TAG: $CI_REGISTRY_IMAGE:dev
      BUILD_VERSION: dev.$CI_COMMIT_SHORT_SHA
  - if: $CI_COMMIT_TAG
    variables:
      STAGE: production
      IMAGE_TAG: $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG
      LATEST_IMAGE_TAG: $CI_REGISTRY_IMAGE:latest
      BUILD_VERSION: $CI_COMMIT_TAG

# composer:
#   stage: .pre
#   image: docker.io/composer:lts
#   script:
#     - composer install
#   tags:
#   - someah-docker
#   only:
#   - dev
#   - main
#   - tags
#   artifacts:
#     paths:
#       - vendor/
#     expire_in: 1 week

# node:
#   stage: .pre
#   image: docker.io/node:20.18-alpine
#   script:
#     - npm install
#     - npm run build
#   tags:
#   - someah-docker
#   only:
#   - dev
#   - main
#   - tags
#   artifacts:
#     paths:
#       - node_modules/
#       - public/build/
#     expire_in: 1 week

# test:
#   stage: test
#   image: docker.io/composer:lts
#   script:
#     - apk add --no-cache autoconf g++ make linux-headers
#     - pecl install xdebug
#     - docker-php-ext-enable xdebug
#     - echo "APP_KEY=" > .env
#     - php artisan key:generate
#     - XDEBUG_MODE=coverage ./vendor/bin/phpunit --coverage-clover coverage-report.xml --log-junit tests-report.xml
#   artifacts:
#     paths:
#       - coverage-report.xml
#       - tests-report.xml
#   tags:
#   - someah-docker
#   rules:
#   - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
#   - if: $CI_COMMIT_BRANCH == 'main'
#   - if: $CI_COMMIT_BRANCH == 'dev'

# code_quality:
#   stage: code-quality
#   variables:
#     SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" # Defines the location of the analysis task cache
#     GIT_DEPTH: "0" # Tells git to fetch all the branches of the project, required by the analysis task
#   image:
#     name: sonarsource/sonar-scanner-cli:11
#     entrypoint: [ "" ]
#   cache:
#     policy: pull-push
#     key: "sonar-cache-$CI_COMMIT_REF_SLUG"
#     paths:
#     - "${SONAR_USER_HOME}/cache"
#     - sonar-scanner/
#   tags:
#   - someah-docker
#   script:
#   - sonar-scanner -Dsonar.host.url="${SONAR_HOST_URL}"
#   allow_failure: true
#   rules:
#   - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
#   - if: $CI_COMMIT_BRANCH == 'main'
#   - if: $CI_COMMIT_BRANCH == 'dev'

.build_configurations: &build_configurations
  image: docker:latest
  services:
  - docker:dind
  tags:
  - someah

base-build:
  stage: base-build
  !!merge <<: *build_configurations
  only:
    changes:
    - base.Dockerfile
    - .docker/*
    refs:
    - main
    - dev
  before_script:
  - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
  - docker build -f base.Dockerfile --pull -t "$CI_REGISTRY_IMAGE:base" .
  - docker push "$CI_REGISTRY_IMAGE:base"

.build-stage:
  stage: build
  environment:
    name: $STAGE
  !!merge <<: *build_configurations
  before_script:
  - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  script:
  - docker build --pull --build-arg "CI_REGISTRY_IMAGE=$CI_REGISTRY_IMAGE" --build-arg "APP_URL=$CI_ENVIRONMENT_URL" --build-arg "APP_NAME=$CI_PROJECT_NAME" --build-arg "BUILD_VERSION=$BUILD_VERSION" -t "$IMAGE_TAG" -t "$LATEST_IMAGE_TAG" .
  - docker push "$IMAGE_TAG"
  - docker push "$LATEST_IMAGE_TAG"

.deploy-stage:
  stage: deploy
  environment:
    name: $STAGE
  variables:
    GIT_STRATEGY: none
  before_script:
  - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  script:
  - cd $HOME_FOLDER
  - if [[ "$CI_ENVIRONMENT_NAME" == "production" ]]; then echo "APP_DEBUG=false"; else echo "APP_DEBUG=true"; fi > .env-app
  - cat $ENV >> .env-app
  - echo -e "\n" >> .env-app
  - cat $GENERAL_ENV >> .env-app
  - grep -q "^IMAGE_URL=" .env && sed -i "s|^IMAGE_URL=.*|IMAGE_URL=$IMAGE_TAG|" .env || echo "IMAGE_URL=$IMAGE_TAG" >> .env
  - docker compose pull 
  - docker compose up -d

.migrate-stage:
  stage: migrate
  environment:
    name: $STAGE
  variables:
    GIT_STRATEGY: none
  only:
    changes:
    - "database/migrations/*"
  script:
  - cd $HOME_FOLDER
  - docker compose exec -T app php artisan migrate

.seed-stage:
  stage: seed
  environment:
    name: $STAGE
  variables:
    GIT_STRATEGY: none
  only:
    changes:
    - "database/seeders/*"
  script:
  - cd $HOME_FOLDER
  - docker compose exec -T app php artisan db:seed
  when: manual

build-development:
  extends: .build-stage
  tags:
  - someah
  only:
  - dev

deploy-development:
  extends: .deploy-stage
  tags:
  - someah
  only:
  - dev

migrate-development:
  extends: .migrate-stage
  script:
  - cd $HOME_FOLDER
  - docker compose exec -T app php artisan migrate
  - docker compose exec -T app-gml php artisan migrate
  tags:
  - someah
  only:
    refs:
    - dev

seed-development:
  extends: .seed-stage
  script:
  - cd $HOME_FOLDER
  - docker compose exec -T app php artisan db:seed
  - docker compose exec -T app-gml php artisan db:seed
  tags:
  - someah
  only:
    refs:
    - dev

fresh migrate seed dev:
  stage: .post
  environment: development
  variables:
    GIT_STRATEGY: none
  tags:
  - someah
  only:
  - dev
  script:
  - cd $HOME_FOLDER
  - docker compose exec -T app php artisan migrate:fresh --seed
  when: manual

fresh migrate seed dev gml:
  stage: .post
  environment: development
  variables:
    GIT_STRATEGY: none
  tags:
  - someah
  only:
  - dev
  script:
  - cd $HOME_FOLDER
  - docker compose exec -T app-gml php artisan migrate:fresh --seed
  when: manual

build-production:
  extends: .build-stage
  tags:
  - someah
  only:
  - tags

deploy-production:
  extends: .deploy-stage
  tags:
  - someah
  only:
  - tags

migrate-production:
  extends: .migrate-stage
  tags:
  - someah
  only:
    refs:
    - tags

seed-production:
  extends: .seed-stage
  tags:
  - someah
  only:
    refs:
    - tags
