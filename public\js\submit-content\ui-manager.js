/**
 * Submit Content UI Manager Module
 * Handles UI updates and page title management
 */

window.SubmitContent = window.SubmitContent || {};

window.SubmitContent.UIManager = {
  
  /**
   * Adjust banner position based on sidebar width
   */
  adjustBannerPosition: function() {
    const state = window.SubmitContent.state;
    const windowWidth = $(window).width();

    if (windowWidth >= 992) { // Desktop view
      const sidebarWidth = $(".app-sidebar").width() || 280;

      // Set the left position to match the sidebar width exactly
      state.selectionBanner.css("left", sidebarWidth + "px");

      // Set the width to fill the remaining space
      state.selectionBanner.css("width", (windowWidth - sidebarWidth) + "px");
    } else { // Mobile view
      // Full width on mobile
      state.selectionBanner.css("left", "0");
      state.selectionBanner.css("width", "100%");
    }

    // Ensure the banner has proper z-index
    state.selectionBanner.css("z-index", "100");
  },

  /**
   * Update page title and badge
   */
  updatePageTitle: function(currentStatus) {
    const state = window.SubmitContent.state;
    const config = window.SubmitContent.config;

    // Update the title and badge
    $("#status-title").text("Submit Content - " + config.statusTitleMap[currentStatus]);

    // Get the count for the current status
    const statusKey = currentStatus.toLowerCase();

    // Make sure contentCounts[statusKey] exists
    if (!state.contentCounts[statusKey]) {
      state.contentCounts[statusKey] = { index: 0, image: 0, video: 0, audio: 0, document: 0 };
    }

    const count = parseInt(state.contentCounts[statusKey].index) || 0;

    // Log the count for debugging
    console.log("Status:", currentStatus, "Count:", count, "Full object:", state.contentCounts[statusKey]);

    // Update the badge count - always show the count, even if it's 0
    $("#status-badge").text(count);

    // Make sure the badge is visible
    $("#status-badge").removeClass('hidden');

    // Set the badge color based on status
    let badgeClass = 'badge-dark';
    if (currentStatus.toLowerCase() === 'draft') badgeClass = 'badge-dark';
    else if (currentStatus.toLowerCase() === 'pending') badgeClass = 'badge-warning';
    else if (currentStatus.toLowerCase() === 'rejected') badgeClass = 'badge-danger';
    else if (currentStatus.toLowerCase() === 'approved') badgeClass = 'badge-primary';
    else if (currentStatus.toLowerCase() === 'published') badgeClass = 'badge-success';

    // Update badge class - always keep the badge visible
    $("#status-badge").removeClass('badge-dark badge-warning badge-danger badge-primary badge-success hidden').addClass(badgeClass);

    // Update the badge text to always show "Approved" in the Approved tab
    if (currentStatus === "Approved") {
      $("#status-title").text("Submit Content - Approved");
    }

    // Show/hide the "Upload Now" button based on the current status
    // Only show it on the "Not Submitted" (Draft) tab
    if (currentStatus === 'Draft') {
      $("#upload-now-button").show();
    } else {
      $("#upload-now-button").hide();
    }

    // Update sidebar menu active state
    this.updateSidebarActiveState(currentStatus);
  },

  /**
   * Update sidebar menu active state
   */
  updateSidebarActiveState: function(currentStatus) {
    const config = window.SubmitContent.config;
    
    // First, remove all active classes from all submenu items
    $("#submit-content-submenu .menu-link").removeClass("bg-gray-50 dark:bg-coal-300 bg-gray-800");

    // Add active class to the current tab's menu item
    const tabParam = config.statusUrlMap[currentStatus];
    $(`#submit-content-submenu a[href*="tab=${tabParam}"]`).addClass("bg-gray-50 dark:bg-coal-300");

    // Remove any bullet points or other active indicators
    $("#submit-content-submenu .menu-item").removeClass("active");

    // Add custom CSS to override the bullet point
    if (!$("#sidebar-bullet-fix").length) {
      $("head").append(`
        <style id="sidebar-bullet-fix">
          #submit-content-submenu .menu-link::before {
            opacity: 0 !important;
          }
          #submit-content-submenu .menu-link.bg-gray-50::before {
            opacity: 1 !important;
          }
        </style>
      `);
    }
  },

  /**
   * Switch tab and update UI state
   */
  switchTab: function(tabElement) {
    const state = window.SubmitContent.state;
    
    if (!tabElement) return;
    
    const newStatus = $(tabElement).data('status');
    console.log('Switching to tab:', newStatus);
    
    // Update status and UI state
    state.status = newStatus;
    state.selectedIds = [];
    
    // Update tab active state
    $(".tab").removeClass("active");
    $(tabElement).addClass("active");
    
    // Update detail content
    const newDetailSelector = "#detail-" + state.status.toLowerCase();
    const newDetailContent = $(newDetailSelector);
    
    if (newDetailContent.length) {
      if (state.detailContent) {
        state.detailContent.html(state.emptyContent);
      }
      state.detailContent = newDetailContent;
      console.log('Detail content updated to:', newDetailSelector);
    } else {
      console.error('Detail content not found:', newDetailSelector);
    }
    
    // Update UI state
    state.btnUpdate.addClass("hidden").removeClass("flex");
    
    // Update filter and page title
    window.SubmitContent.ContentManager.updateFilter(state.filter, state.status);
    this.updatePageTitle(state.status);
  },

  /**
   * Update URL without reloading the page
   */
  updateURL: function(params) {
    const newUrl = new URL(window.location.href);
    
    Object.keys(params).forEach(key => {
      newUrl.searchParams.set(key, params[key]);
    });
    
    window.history.pushState({}, '', newUrl);
  }
};
